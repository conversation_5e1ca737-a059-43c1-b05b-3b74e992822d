using System.Reflection;

namespace TeyaMobile;

public partial class Message : ContentPage
{
    public string EditorContent { get; set; }
	public Message()
	{
		InitializeComponent();
        BindingContext = this;
        //SecureStorage.GetAsync("auth_token");
        var token = SecureStorage.GetAsync("auth_token");
		var res = token.Result; //use access token from here
	}

    private async void OnTranscribeClicked(object sender, EventArgs e)
    {
        var patientId = Guid.Parse("24E73BD4-1EA8-403C-98CD-DB2DFE6FA1D0"); //_patientService.SelectedPatient.PatientId;
        var orgId = Guid.Parse("00000000-0000-0000-0000-000000000000"); //Guid.Parse(_patientService.SelectedPatient.PCPID); // or _user.id if from auth

        await Shell.Current.GoToAsync($"//TemplatesPage?patientId={patientId}&orgId={orgId}");
        /*await Shell.Current.GoToAsync($"//{nameof(TemplatesPage)}", new Dictionary<string, object>
            {
                { "patientId", patientId },
                { "orgId", orgId }
            });*/

    }
}