﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IPageRoleMappingService
    {
        Task<IEnumerable<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId, bool Subscription);
        Task<IEnumerable<PageRoleMappingData>> GetPagesByRoleIdAsync(Guid roleId, Guid OrgID, bool Subscription);
        Task<IEnumerable<PageRoleMappingData>> GetPageRoleMappingsAsync();
        Task<PageRoleMappingData> GetPageRoleMappingByIdAsync(Guid id, Guid OrgID, bool Subscription);
        Task AddPageRoleMappingAsync(PageRoleMappingData pageRoleMapping);
        Task UpdatePageRoleMappingAsync(PageRoleMappingData pageRoleMapping);
        Task DeletePageRoleMappingByIdAsync(Guid id, Guid OrgID, bool Subscription);
    }
}
