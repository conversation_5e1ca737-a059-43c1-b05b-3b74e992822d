﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileModel.Model
{
    public class Surgical : IModel
    {
        public Guid SurgeryId { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public string? Surgery { get; set; }
        public Guid PCPId { get; set; }
        public bool IsActive { get; set; }
    }
}
