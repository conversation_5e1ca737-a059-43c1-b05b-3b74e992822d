﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.TeyaUIViewModelResources;

namespace TeyaMobileViewModel.ViewModel
{
    public class HistoryOfPresentIllnessService : IHistoryOfPresentIllnessService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public HistoryOfPresentIllnessService(
            HttpClient httpClient,
            IConfiguration configuration,
            IStringLocalizer<TeyaUIViewModelsStrings> localizer,
            ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all the HPI records by Patient ID
        /// </summary>
        /// <param name="patientId"></param>
        /// <returns></returns>
        public async Task<List<HistoryOfPresentIllness>> GetHpiByPatientIdAsync(Guid patientId)
        {
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/HistoryOfPresentIllness/patient/{patientId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadFromJsonAsync<List<HistoryOfPresentIllness>>() ?? new List<HistoryOfPresentIllness>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to fetch HPI records: {ex.Message}");
                return new List<HistoryOfPresentIllness>();
            }
        }

        /// <summary>
        /// Get only the active HPI records by Patient ID
        /// </summary>
        /// <param name="patientId"></param>
        /// <returns></returns>
        public async Task<List<HistoryOfPresentIllness>> GetActiveHpiByPatientIdAsync(Guid patientId)
        {
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/HistoryOfPresentIllness/patient/{patientId}/active";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadFromJsonAsync<List<HistoryOfPresentIllness>>() ?? new List<HistoryOfPresentIllness>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to fetch active HPI records: {ex.Message}");
                return new List<HistoryOfPresentIllness>();
            }
        }
        /// <summary>
        /// Get  Specific HPI record by ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<HistoryOfPresentIllness> GetHpiByIdAsync(Guid id)
        {
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/HistoryOfPresentIllness/{id}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadFromJsonAsync<HistoryOfPresentIllness>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to fetch HPI by ID: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Add HPI records
        /// </summary>
        /// <param name="hpiRecords"></param>
        /// <returns></returns>
        public async Task AddHpiAsync(List<HistoryOfPresentIllness> hpiRecords)
        {
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/HistoryOfPresentIllness/AddHPI";
                var bodyContent = JsonSerializer.Serialize(hpiRecords);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to add HPI records: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete HPI record by ID
        /// </summary>
        /// <param name="hpiId"></param>
        /// <returns></returns>
        public async Task DeleteHpiAsync(Guid hpiId)
        {
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/HistoryOfPresentIllness/{hpiId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                Console.WriteLine($"[INFO] Successfully soft deleted HPI record with ID {hpiId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to soft delete HPI record: {ex.Message}");
            }
        }
        /// <summary>
        /// Update HPI record by Entity
        /// </summary>
        /// <param name="hpiRecord"></param>
        /// <returns></returns>
        public async Task UpdateHpiAsync(HistoryOfPresentIllness hpiRecord)
        {
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/HistoryOfPresentIllness/{hpiRecord.Id}";
                var content = new StringContent(JsonSerializer.Serialize(hpiRecord), Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to update HPI record: {ex.Message}");
            }
        }

        /// <summary>
        /// Update Multiple HPI records 
        /// </summary>
        /// <param name="hpiRecords"></param>
        /// <returns></returns>
        public async Task UpdateHpiListAsync(List<HistoryOfPresentIllness> hpiRecords)
        {
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/HistoryOfPresentIllness/bulk-update";
                var content = new StringContent(JsonSerializer.Serialize(hpiRecords), Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to update HPI records: {ex.Message}");
            }
        }
    }
}