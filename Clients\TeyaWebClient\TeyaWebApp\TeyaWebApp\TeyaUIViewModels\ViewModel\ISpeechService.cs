﻿namespace TeyaUIViewModels.ViewModel
{
    public interface ISpeechService
    {
        Task StartTranscriptionAsync(Guid id);
        Task StopTranscriptionAsync(Guid Id, Guid patientId, String VisitType, Guid? OrgID, bool Subscription);
        Task PostTranscriptionsAsync(Guid Id, Guid patientId, String VisitType, Guid? OrgID, bool Subscription);
        Task ProcessAudioChunk(string base64AudioChunk);
    }
}
