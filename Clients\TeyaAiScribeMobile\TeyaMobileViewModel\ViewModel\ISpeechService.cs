﻿using System;
using System.Threading.Tasks;
using Microsoft.CognitiveServices.Speech;

namespace TeyaMobileViewModel.ViewModel
{
    public interface ISpeechService : IDisposable
    {
        event EventHandler<string>? OnPartialTranscription;
        event EventHandler<string>? OnFinalTranscription;
        event EventHandler<Exception>? OnError;

        // Properties
        string TotalTranscribed { get; set; }
        string SpeechData { get; set; }

        // Methods
        Guid GetCurrentRecordingId();
        Task ProcessAudioChunk(string base64AudioChunk);
        Task StartTranscriptionAsync(Guid id);
        Task StopTranscriptionAsync(Guid id, Guid patientId, string visitType, Guid? orgid, bool sub);
        Task PostTranscriptionsAsync(Guid id, Guid patientId, string visitType, Guid? orgId, bool subscription);
        Task UploadAudioToBackendAsync(string filePath);
        Task StartContinuousRecognitionAsync();
        Task StopContinuousRecognitionAsync();
        Task<string> TranscribeAudioFileAsync(string audioFilePath);
    }
}