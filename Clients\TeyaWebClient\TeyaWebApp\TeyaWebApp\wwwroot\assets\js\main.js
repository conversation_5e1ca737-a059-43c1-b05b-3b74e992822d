window.onscroll = function() {myFunction()};
var header = document.getElementById("header");
var sticky = header.offsetTop;
function myFunction() {
  if (window.pageYOffset > sticky) {
    header.classList.add("sticky");
  } else {
    header.classList.remove("sticky");
  }
}

const navLinks = document.querySelectorAll('.nav-link');
navLinks.forEach(link => {
    link.addEventListener('click', function () {
        navLinks.forEach(link => link.classList.remove('active'));
        this.classList.add('active');
    });
});

$(document).ready(function () {
  var backToTop = $('#back-to-top');

  $(window).scroll(function () {
      if ($(this).scrollTop() > 300) {
          backToTop.addClass('show');
      } else {
          backToTop.removeClass('show');
      }
  });

  backToTop.click(function (e) {
      e.preventDefault();
      $('html, body').animate({ scrollTop: 0 }, 600);
  });
});



$(document).ready(function () {
  $(".animate__animated").each(function () {
      let $this = $(this);

      // Store original animate classes except animate__animated
      let originalClasses = ($this.attr("class").match(/animate__\S+/g) || []).filter(c => c !== "animate__animated");

      if (originalClasses.length) {
          // Remove all animate__* classes except animate__animated
          $this.removeClass(originalClasses.join(" "));

          // Store the original classes in a data attribute for later use
          $this.data("originalAnimateClasses", originalClasses.join(" "));
      }
  });

  // Function to add stored animation classes when element is in viewport
  function addAnimationOnScroll(entries, observer) {
      entries.forEach(entry => {
          if (entry.isIntersecting) {
              let $target = $(entry.target);
              let originalClasses = $target.data("originalAnimateClasses");

              if (originalClasses) {
                  $target.addClass(originalClasses);
              }
          }
      });
  }

  // Set up IntersectionObserver
  let observer = new IntersectionObserver(addAnimationOnScroll, {
      root: null, // viewport
      threshold: 0.1 // Trigger when 10% is visible
  });

  // Observe elements
  $(".animate__animated").each(function () {
      observer.observe(this);
  });
});
