﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IOfficeVisitService
    {
        Task<List<Appointment>> GetAppointmentsByuserIdAsync(Guid userid, Guid? OrgID, bool Subscription);
        Task<List<Office_visit_members>> GetMembersByUserIdsAsync(List<Guid> patientIds, Guid? OrgID, bool Subscription);
        Task<List<OfficeVisitModel>> GetPatientListByIdAsync(Guid userId, Guid? OrgID, bool Subscription);
    }
}
