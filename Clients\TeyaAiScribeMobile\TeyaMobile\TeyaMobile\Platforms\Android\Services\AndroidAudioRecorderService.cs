using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.Media;
using Android.OS;
using AndroidX.Core.App;
using AndroidX.Core.Content;
using Java.IO;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;    

namespace TeyaMobile.Platforms.Android.Services
{
    public class AndroidAudioRecorderService : IAudioRecorder
    {
        private MediaRecorder? _mediaRecorder;
        private string _filePath = string.Empty;
        private DateTime _startTime;
        private TimeSpan _pausedDuration = TimeSpan.Zero;
        private DateTime? _pauseTime;
        private bool _isPaused = false;
        private RecordingState _recordingState = RecordingState.Stopped;
        private readonly ILogger<AndroidAudioRecorderService> _logger;
        private Guid? _nextRecordingId;

        public RecordingState RecordingState
        {
            get => _recordingState;
            private set
            {
                if (_recordingState != value)
                {
                    _recordingState = value;
                    RecordingStateChanged?.Invoke(this, _recordingState);
                }
            }
        }

        public event EventHandler<RecordingState>? RecordingStateChanged;
        public event EventHandler<Exception>? ErrorOccurred;

        /// <summary>
        /// Initializes a new instance of the AndroidAudioRecorderService class.
        /// </summary>
        public AndroidAudioRecorderService(ILogger<AndroidAudioRecorderService> logger)
        {
            _logger = logger;
            _logger.LogInformation("AndroidAudioRecorderService initialized");
        }

        private const int REQUEST_RECORD_AUDIO_PERMISSION = 200;
        private readonly string[] _permissions = { global::Android.Manifest.Permission.RecordAudio,
                                                 global::Android.Manifest.Permission.WriteExternalStorage,
                                                 global::Android.Manifest.Permission.ReadExternalStorage };

        /// <summary>
        /// Checks and requests the necessary permissions for audio recording.
        /// </summary>
        private async Task<bool> CheckAndRequestPermissionsAsync()
        {
            try
            {
                _logger.LogInformation("Checking permissions...");
                var activity = Platform.CurrentActivity;
                if (activity == null)
                {
                    _logger.LogError("Current activity is null");
                    throw new InvalidOperationException("Current activity is null");
                }
                var recordPermissionStatus = ContextCompat.CheckSelfPermission(activity, global::Android.Manifest.Permission.RecordAudio);
                var writePermissionStatus = ContextCompat.CheckSelfPermission(activity, global::Android.Manifest.Permission.WriteExternalStorage);
                var readPermissionStatus = ContextCompat.CheckSelfPermission(activity, global::Android.Manifest.Permission.ReadExternalStorage);
                _logger.LogInformation("Permission status - Record: {RecordStatus}, Write: {WriteStatus}, Read: {ReadStatus}",
                    recordPermissionStatus, writePermissionStatus, readPermissionStatus);
                if (recordPermissionStatus == Permission.Granted)
                {
                    _logger.LogInformation("All required permissions granted");
                    return true;
                }
                _logger.LogInformation("Requesting permissions using MAUI API...");
                var status = await Permissions.RequestAsync<Permissions.Microphone>();
                if (status == PermissionStatus.Granted)
                {
                    _logger.LogInformation("Microphone permission granted");
                    return true;
                }
                else
                {
                    _logger.LogWarning("Microphone permission denied: {Status}", status);
                    _logger.LogInformation("Permission denied by user. Will not request again in this session.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permissions");
                return false;
            }
        }

        public void SetNextRecordingId(Guid recordingId)
        {
            _nextRecordingId = recordingId;
        }

        private string GetRecordingFileName()
        {
            var ext = ".webm";
            if (_nextRecordingId.HasValue)
            {
                return $"{_nextRecordingId.Value}{ext}";
            }
            return $"recording_{DateTime.Now:yyyyMMdd_HHmmss}{ext}";
        }

        /// <summary>
        /// Starts a new audio recording.
        /// </summary>
        public async Task StartRecordingAsync()
        {
            try
            {
                if (RecordingState == RecordingState.Recording)
                    return;
                bool permissionsGranted = await CheckAndRequestPermissionsAsync();
                if (!permissionsGranted)
                {
                    throw new InvalidOperationException("Microphone permission denied");
                }
                string fileName = GetRecordingFileName();
                var context = Platform.AppContext;
                Java.IO.File externalFilesDir = context.GetExternalFilesDir(null);
                Java.IO.File appDir = new Java.IO.File(externalFilesDir, "Recordings");
                if (!appDir.Exists())
                    appDir.Mkdirs();
                Java.IO.File file = new Java.IO.File(appDir, fileName);
                _filePath = file.AbsolutePath;
                _logger.LogInformation("Recording to file: {FilePath}", _filePath);
                try
                {
                    ReleaseMediaRecorder();
                    await Task.Delay(500);
                    _mediaRecorder = new MediaRecorder();
                    try
                    {
                        _logger.LogDebug("Setting audio source...");
                        _mediaRecorder.SetAudioSource(AudioSource.Mic);
                        _logger.LogDebug("Setting output format...");
                        _mediaRecorder.SetOutputFormat(OutputFormat.AmrNb);
                        _logger.LogDebug("Setting audio encoder...");
                        _mediaRecorder.SetAudioEncoder(AudioEncoder.AmrNb);
                        _logger.LogDebug("Setting audio encoding bit rate...");
                        _mediaRecorder.SetAudioEncodingBitRate(16000);
                        _logger.LogDebug("Setting audio sampling rate...");
                        _mediaRecorder.SetAudioSamplingRate(16000);
                        _logger.LogDebug("Setting number of channels (mono)...");
                        _logger.LogDebug("Setting output file...");
                        _mediaRecorder.SetOutputFile(_filePath);
                        _logger.LogDebug("Preparing recorder...");
                        _mediaRecorder.Prepare();
                        _logger.LogDebug("Starting recording...");
                        _mediaRecorder.Start();
                        _startTime = DateTime.Now;
                        _pausedDuration = TimeSpan.Zero;
                        _pauseTime = null;
                        RecordingState = RecordingState.Recording;
                        _logger.LogInformation("Recording started successfully");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "MediaRecorder configuration error");
                        ReleaseMediaRecorder();
                        throw new InvalidOperationException($"Failed to configure recorder: {ex.Message}", ex);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "MediaRecorder error");
                    ReleaseMediaRecorder();
                    throw new InvalidOperationException($"Failed to start recording: {ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Recording error");
                ErrorOccurred?.Invoke(this, ex);
                ReleaseMediaRecorder();
                throw;
            }
        }

        /// <summary>
        /// Pauses the current recording if supported.
        /// </summary>
        public async Task PauseRecordingAsync()
        {
            if (RecordingState != RecordingState.Recording || !SupportsPauseResume())
                return;
            try
            {
                if (Build.VERSION.SdkInt >= BuildVersionCodes.N)
                {
                    await Task.Run(() => _mediaRecorder?.Pause());
                    _pauseTime = DateTime.Now;
                    _isPaused = true;
                    RecordingState = RecordingState.Paused;
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, ex);
                throw;
            }
        }

        /// <summary>
        /// Resumes a paused recording if supported.
        /// </summary>
        public async Task ResumeRecordingAsync()
        {
            if (RecordingState != RecordingState.Paused || !SupportsPauseResume())
                return;
            try
            {
                if (Build.VERSION.SdkInt >= BuildVersionCodes.N)
                {
                    await Task.Run(() => _mediaRecorder?.Resume());
                    if (_pauseTime.HasValue)
                    {
                        _pausedDuration += DateTime.Now - _pauseTime.Value;
                        _pauseTime = null;
                    }
                    _isPaused = false;
                    RecordingState = RecordingState.Recording;
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, ex);
                throw;
            }
        }

        /// <summary>
        /// Stops the current recording and returns the file path.
        /// </summary>
        public async Task<string> StopRecordingAsync()
        {
            if (RecordingState == RecordingState.Stopped)
                return string.Empty;
            try
            {
                RecordingState = RecordingState.Processing;
                string currentFilePath = _filePath;
                await Task.Run(() =>
                {
                    try
                    {
                        if (_mediaRecorder != null)
                        {
                            _logger.LogDebug("Stopping recording...");
                            _mediaRecorder.Stop();
                            _logger.LogDebug("Recording stopped successfully");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error stopping recording");
                    }
                    finally
                    {
                        ReleaseMediaRecorder();
                    }
                });
                RecordingState = RecordingState.Stopped;
                if (!string.IsNullOrEmpty(currentFilePath) && System.IO.File.Exists(currentFilePath))
                {
                    _logger.LogInformation("Recording saved to: {FilePath}", currentFilePath);
                    return currentFilePath;
                }
                else
                {
                    _logger.LogWarning("Recording file not found");
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in StopRecordingAsync");
                ErrorOccurred?.Invoke(this, ex);
                ReleaseMediaRecorder();
                RecordingState = RecordingState.Stopped;
                throw;
            }
        }

        /// <summary>
        /// Gets the current recording duration in seconds.
        /// </summary>
        public double GetRecordingDuration()
        {
            if (RecordingState == RecordingState.Stopped)
                return 0;
            TimeSpan duration;
            if (_isPaused && _pauseTime.HasValue)
            {
                duration = (_pauseTime.Value - _startTime) - _pausedDuration;
            }
            else
            {
                duration = (DateTime.Now - _startTime) - _pausedDuration;
            }
            return duration.TotalSeconds;
        }

        /// <summary>
        /// Checks if the platform supports pausing and resuming recordings.
        /// </summary>
        public bool SupportsPauseResume()
        {
            return Build.VERSION.SdkInt >= BuildVersionCodes.N;
        }

        /// <summary>
        /// Gets the file extension used by this recorder.
        /// </summary>
        public string GetFileExtension()
        {
            return ".webm";
        }

        /// <summary>
        /// Releases the MediaRecorder resources.
        /// </summary>
        private void ReleaseMediaRecorder()
        {
            if (_mediaRecorder != null)
            {
                try
                {
                    try
                    {
                        _mediaRecorder.Stop();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Error stopping MediaRecorder");
                    }
                    try
                    {
                        _mediaRecorder.Release();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Error releasing MediaRecorder");
                    }
                    try
                    {
                        _mediaRecorder.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Error disposing MediaRecorder");
                    }
                }
                finally
                {
                    _mediaRecorder = null;
                }
            }
        }
    }
}