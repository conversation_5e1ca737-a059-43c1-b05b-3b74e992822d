﻿using System.Text.Json.Serialization;

namespace TeyaMobileModel.Model
{
    public class Product : IModel
    {
        [JsonPropertyName("Id")]
        public Guid Id { get; set; }

        [JsonPropertyName("Name")]
        public string? Name { get; set; }

        [JsonPropertyName("Description")]
        public string? Description { get; set; }

        [JsonPropertyName("Byproduct")]
        public string? Byproduct { get; set; }
    }
}
