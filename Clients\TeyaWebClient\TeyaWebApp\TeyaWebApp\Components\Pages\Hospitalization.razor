﻿@page "/Hospitalization"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject HttpClient Http
@inject ISnackbar Snackbar
@using Syncfusion.Blazor.RichTextEditor
@inject TeyaUIViewModels.ViewModel.IHospitalizationRecordService HospitalizationRecordService


<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" aria-label="delete" @onclick="OpenAddTaskDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>


<MudDialog @ref="_addMemberDialog" Style="width: 75vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">

    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Hospitalization"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column; ">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <SfGrid @ref="hospitalizationRecordGrid" TValue="HospitalizationRecord" DataSource="@records"
                        Toolbar="@ToolbarItems" AllowPaging="true"
                        GridLines="GridLine.Both"
                        Style="font-size: 0.85rem; margin-top: 24px;">

                    <GridEditSettings AllowEditing="true" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>

                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="HospitalizationRecord"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="RecordID" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field=@nameof(HospitalizationRecord.Date) HeaderText="@Localizer["Date"]" TextAlign="TextAlign.Center" Format="MM/dd/yyyy" Width="160">
                            <EditTemplate>
                                <SfDatePicker TValue="DateTime?"
                                              @bind-Value="@((context as HospitalizationRecord).Date)"
                                              Max="@DateTime.Today"
                                              Placeholder="Select date"
                                              ShowClearButton="false">
                                </SfDatePicker>
                            </EditTemplate>
                        </GridColumn>
                        <GridColumn Field=@nameof(HospitalizationRecord.Reason) HeaderText="@Localizer["Reason"]" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left" ValidationRules="@(new Syncfusion.Blazor.Grids.ValidationRules { Required = true })"></GridColumn>
                        <GridColumn HeaderText="Actions"  TextAlign="TextAlign.Center" Width="120">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions()
                                             { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>