﻿
using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobileModel.ViewModel
{
    public class RoleService : IRoleService
    {
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<RoleService> _localizer;
        private readonly ILogger<RoleService> _logger;

        public RoleService(HttpClient httpClient, IStringLocalizer<RoleService> localizer, ILogger<RoleService> logger)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            Env.Load();
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        public async Task<Role> RegisterRoleAsync(Role role)
        {
            try
            {
                var bodyContent = JsonSerializer.Serialize(role);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/Roles";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);

                    try
                    {
                        return JsonSerializer.Deserialize<Role>(responseData);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, _localizer["JsonDeserializationError", ex.Message]);
                        throw;
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    _logger.LogError("Role already exists with status code {StatusCode}", response.StatusCode);
                    throw new HttpRequestException(_localizer["RoleAlreadyExists"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Registration failed. Status Code: {response.StatusCode}, Reason: {response.ReasonPhrase}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RegistrationFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["RoleRegistrationError"]);
                throw;
            }
        }

        public async Task<Role> GetRoleByIdAsync(Guid roleId, Guid OrgID, bool Subscription)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Roles/{roleId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    return JsonSerializer.Deserialize<Role>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingRoleById"], roleId);
                throw;
            }
        }

        public async Task<List<Role>> GetAllRolesAsync()
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Roles/";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Role>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllRoles"]);
                throw;
            }
        }

        public async Task DeleteRoleByIdAsync(Guid roleId, Guid OrgID, bool Subscription)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Roles/{roleId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["RoleDeletedSuccessfully"], roleId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RoleDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingRole"], roleId);
                throw;
            }
        }

        public async Task UpdateRoleByIdAsync(Guid roleId, Role role)
        {
            if (role == null || role.RoleId != roleId)
            {
                _logger.LogError(_localizer["InvalidRole"]);
                throw new ArgumentException(_localizer["InvalidRole"]);
            }

            try
            {
                var apiUrl = $"{_MemberService}/api/Roles/{roleId}";
                var bodyContent = JsonSerializer.Serialize(role);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["UpdateSuccessful"], roleId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingRole"], roleId);
                throw;
            }
        }
        public async Task<List<Role>> GetRolesByNameAsync(string name, Guid OrgID, bool Subscription)
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Roles/search/{OrgID}/{Subscription}?name={Uri.EscapeDataString(name)}");
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<List<Role>>() ?? new List<Role>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorGettingRoles"], name);
                throw;
            }
        }

        public async Task<List<Role>> GetAllRolesByOrgIdAsync(Guid? ID, bool Subscription)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Roles/Org/{ID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Role>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllRoles"]);
                throw;
            }
        }
    }
}
