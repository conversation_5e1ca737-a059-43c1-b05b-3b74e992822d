﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileModel.Model
{
    public class MedicalHistory
    {
        public Guid MedicalHistoryID { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? History { get; set; }
        public Guid PCPId { get; set; }
        public string? ICDCode { get; set; }
        public bool? IsPregnant { get; set; }
        public bool? IsBreastFeeding { get; set; }
        public bool IsActive { get; set; }
    }
}