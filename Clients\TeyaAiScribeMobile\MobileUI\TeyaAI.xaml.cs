using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;
using Microsoft.Maui.Controls;

namespace TeyaAiScribeMobile
{
    public partial class TeyaAI : ContentPage
    {
        private readonly IAudioRecorder _audioRecorder;
        private readonly ISpeechService _speechService;
        private readonly ILogger<TeyaAI> _logger;

        private bool _isPaused = false;
        private System.Timers.Timer _timer;
        private DateTime _startTime;
        private Guid _currentRecordingId;

        public TeyaAI(IAudioRecorder audioRecorder, ISpeechService speechService, ILogger<TeyaAI> logger)
        {
            InitializeComponent();
            _audioRecorder = audioRecorder;
            _speechService = speechService;
            _logger = logger;

            InitializeRecordingEvents();
            InitializeSpeechEvents();
            InitializeTimer();
        }

        #region Initialization

        private void InitializeRecordingEvents()
        {
            _audioRecorder.RecordingStateChanged += OnRecordingStateChanged;
            _audioRecorder.ErrorOccurred += OnErrorOccurred;
        }

        private void InitializeSpeechEvents()
        {
            _speechService.OnPartialTranscription += OnPartialTranscription;
            _speechService.OnFinalTranscription += OnFinalTranscription;
            _speechService.OnError += OnSpeechError;
        }

        private void InitializeTimer()
        {
            _timer = new System.Timers.Timer(1000);
            _timer.Elapsed += (sender, e) =>
            {
                var elapsed = DateTime.Now - _startTime;
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    TimerLabel.Text = elapsed.ToString(@"mm\:ss");
                });
            };
        }

        #endregion

        #region Button Click Handlers

        private async void OnStartClicked(object sender, EventArgs e)
        {
            try
            {   
                _currentRecordingId = Guid.NewGuid();
                _audioRecorder.SetNextRecordingId(_currentRecordingId);

                await _audioRecorder.StartRecordingAsync();
                await _speechService.StartContinuousRecognitionAsync();

                _startTime = DateTime.Now;
                _timer.Start();
                TranscriptionEditor.Text = string.Empty;

                StartButton.IsVisible = false;
                PauseResumeButton.IsVisible = true;
                StopButton.IsVisible = true;
                StatusLabel.Text = "Recording...";
                PauseResumeButton.Text = "Pause";
                TranscriptionLabel.IsVisible = true;
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to start recording: {ex.Message}", "OK");
                _logger.LogError(ex, "Start recording error");
            }
        }

        private async void OnPauseResumeClicked(object sender, EventArgs e)
        {
            if (_isPaused)
            {
                // Resume
                await _audioRecorder.ResumeRecordingAsync();
                await _speechService.StartContinuousRecognitionAsync();

                StatusLabel.Text = "Recording...";

                _startTime = DateTime.Now;
                _timer.Start();
                PauseResumeButton.Text = "Pause";
                _isPaused = false;
            }
            else
            {
                // Pause
                await _audioRecorder.PauseRecordingAsync();
                await _speechService.StopContinuousRecognitionAsync();

                StatusLabel.Text = "Recording paused";

                _timer.Stop();
                PauseResumeButton.Text = "Resume";
                _isPaused = true;
            }
        }

        private async void OnStopClicked(object sender, EventArgs e)
        {
            try
            {
                _timer.Stop();
                var filePath = await _audioRecorder.StopRecordingAsync();
                await _speechService.StopContinuousRecognitionAsync();

                GifAnimation.IsAnimationPlaying = false;

                StartButton.IsVisible = true;
                PauseResumeButton.IsVisible = false;
                StopButton.IsVisible = false;
                PauseResumeButton.Text = "Pause";
                _isPaused = false;

                if (!string.IsNullOrEmpty(filePath))
                {
                    StatusLabel.Text = "Processing...";
                    TranscriptionLabel.Text = "Processing...";

                    try
                    {
                        await _speechService.UploadAudioToBackendAsync(filePath);

                        Guid patientId = Guid.NewGuid();
                        string visitType = "Unknown";   
                        Guid? orgId = new Guid("1B9C038A-3AAA-4783-A1F7-BB574E1F1BCB");  
                        bool subscription = false;      

                        await _speechService.PostTranscriptionsAsync(
                            _currentRecordingId,
                            patientId,
                            visitType,
                            orgId,
                            subscription
                        );

                        StatusLabel.Text = "Teya AI Scribe";
                        TranscriptionLabel.Text = "Tap on Transcribe";
                    }
                    catch (Exception uploadEx)
                    {
                        StatusLabel.Text = "Teya AI Scribe";
                        TranscriptionLabel.Text = "Ready";

                        await DisplayAlert("Upload Error", $"Failed to upload: {uploadEx.Message}", "OK");
                        _logger.LogError(uploadEx, "Upload error");
                    }
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to stop recording: {ex.Message}", "OK");
                _logger.LogError(ex, "Stop recording error");
            }
        }

        #endregion

        #region Event Handlers

        private void OnRecordingStateChanged(object sender, RecordingState state)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                switch (state)
                {
                    case RecordingState.Stopped:
                        StatusLabel.Text = "Recording stopped.";
                        break;
                    case RecordingState.Recording:
                        StatusLabel.Text = "Recording...";
                        GifAnimation.IsAnimationPlaying = true;
                        break;
                    case RecordingState.Paused:
                        StatusLabel.Text = "Recording paused.";
                        GifAnimation.IsAnimationPlaying = false;
                        break;
                }
            });
        }

        private void OnPartialTranscription(object sender, string text)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                TranscriptionEditor.Text += text + " ";
                TranscriptionLabel.Text = text;
            });
        }

        private void OnFinalTranscription(object sender, string text)
        {
            // Optional: append to final result
        }

        private void OnSpeechError(object sender, Exception ex)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await DisplayAlert("Speech Error", ex.Message, "OK");
            });
        }

        private void OnErrorOccurred(object sender, Exception ex)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await DisplayAlert("Recording Error", ex.Message, "OK");
            });
        }

        #endregion

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            _timer?.Stop();
            _audioRecorder.RecordingStateChanged -= OnRecordingStateChanged;
            _audioRecorder.ErrorOccurred -= OnErrorOccurred;
            _speechService.OnPartialTranscription -= OnPartialTranscription;
            _speechService.OnFinalTranscription -= OnFinalTranscription;
            _speechService.OnError -= OnSpeechError;
        }
    }
}