<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:TeyaMobile"  
             x:Class="TeyaMobile.Message"
             Title="Message">
    <Grid>  
        <Grid.RowDefinitions>  
            <RowDefinition Height="Auto" />  
            <RowDefinition Height="Auto" />  
            <RowDefinition Height="*" />  
            <RowDefinition Height="Auto" />  
        </Grid.RowDefinitions>  

        <Label 
            Text="Welcome to TeyaAI"  
            VerticalOptions="Center"   
            HorizontalOptions="Center"  
            Grid.Row="0" />  

        <Button x:Name="TranscribeButton"  
                Text="Transcribe"  
                Clicked="OnTranscribeClicked"  
                IsVisible="True"  
            VerticalOptions="Center" 
                HorizontalOptions="Center"   
                BackgroundColor="Black"  
                Grid.Row="1" />  
        
    </Grid>  
</ContentPage>