using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IAlertService
    {
        Task<List<Alert>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<Alert>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddAlertsAsync(List<Alert> alerts, Guid? OrgID, bool Subscription);
        Task UpdateAlertAsync(Alert alert, Guid? OrgID, bool Subscription);
        Task UpdateAlertsListAsync(List<Alert> alerts, Guid? OrgID, bool Subscription);
        Task DeleteAlertByEntityAsync(Alert alert, Guid? OrgID, bool Subscription);
    }
}
