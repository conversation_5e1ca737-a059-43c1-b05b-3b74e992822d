﻿using DotNetEnv;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.TeyaUIViewModelResources;

namespace TeyaMobileViewModel.ViewModel
{
    public class RecordService : IRecordService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _EncounterNotes;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly ILogger<RecordService> _logger;
        public RecordService(HttpClient httpClient, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ILogger<RecordService> logger, ITokenService tokenService)
        {
            _tokenService = tokenService;
            Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }

        public async Task<Record> GetRecordByIdAsync(Guid recordId)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Records/{recordId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                var records = JsonSerializer.Deserialize<Record>(responseData, options);
                return records;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingMemberById"], recordId);
                throw;
            }
        }
    }
}
