﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IAddressService
    {
      
        Task<Address?> GetAddressByIdAsync(Guid id);

        Task<bool> AddAddressAsync(Address address);

        Task<bool> UpdateAddressAsync(Guid id, Address address);

        Task<bool> DeleteAddressAsync(Guid id);

        Task<List<Address>?> GetAddressesByNameAsync(string name);

        Task<List<Address>?> GetAllAddressesAsync();
    }
}
