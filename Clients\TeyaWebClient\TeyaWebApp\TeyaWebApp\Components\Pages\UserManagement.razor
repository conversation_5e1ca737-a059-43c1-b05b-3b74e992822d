﻿@page "/usermanagement"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "usermanagementAccessPolicy")]
@using System.Text.Json
@using BusinessLayer.Services
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@using TeyaWebApp.Services
@inject GraphApiService customAuthenticationService
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject IDialogService DialogService
@inject IRoleService RolesService
@inject IOrganizationService OrganizationService
@using TeyaUIViewModels
@using TeyaUIModels.ViewModel

<GenericCard Heading="@Localizer["UserManagementHeading"]">
    <MudGrid>
        <MudItem xs="12" md="12" Class="d-flex justify-content-start">
            <MudButton Color="Color.Primary" OnClick="OpenDialogAsync" Class="me-2">
                @Localizer["AddMember"]
            </MudButton>
        </MudItem>
    </MudGrid>
    <MudDivider />
    <SfGrid @ref="UserGrid" Toolbar="@ToolBarItems" DataSource="@allMembers"
            AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both" >

        <GridEvents OnToolbarClick="OnToolbarItemClicked"
                    OnActionBegin="OnActionBegin"
                    OnActionComplete="OnActionComplete" TValue="Member" />
        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal" />

        <GridColumns>
            <GridColumn Field="Email" HeaderText="@Localizer["Email"]" TextAlign="TextAlign.Left" Width="150" IsPrimaryKey="true" />
            <GridColumn Field="UserName" HeaderText="@Localizer["User"]" TextAlign="TextAlign.Left" Width="150" AllowEditing="false" />
            <GridColumn Field="Country" HeaderText="@Localizer["Country"]" TextAlign="TextAlign.Left" Width="150" AllowEditing="false" />
            <GridColumn Field="OrganizationName" HeaderText="@Localizer["OrganizationName"]"
                        TextAlign="TextAlign.Left" Width="150" AllowEditing="false" />
            <GridColumn Field="RoleName" HeaderText="@Localizer["RoleName"]" TextAlign="TextAlign.Left" Width="150"
                        EditTemplate="@RoleNameEditTemplate">
            </GridColumn>
        </GridColumns>
    </SfGrid>
</GenericCard>