﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICustomProcedureAlertService
    {
        Task<List<CustomProcedureAlerts>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<CustomProcedureAlerts>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddCustomProcedureAlertsAsync(List<CustomProcedureAlerts> customProcedureAlerts, Guid? OrgID, bool Subscription);
        Task UpdateCustomProcedureAlertAsync(CustomProcedureAlerts customProcedureAlert, Guid? OrgID, bool Subscription);
        Task UpdateCustomProcedureAlertsListAsync(List<CustomProcedureAlerts> customProcedureAlerts, Guid? OrgID, bool Subscription);
        Task DeleteCustomProcedureAlertByEntityAsync(CustomProcedureAlerts customProcedureAlert, Guid? OrgID, bool Subscription);
    }
}
