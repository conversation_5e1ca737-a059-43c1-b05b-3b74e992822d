using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Schedule;
using Microsoft.AspNetCore.Components;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModels;
using MudBlazor;
using Microsoft.Extensions.Logging;
using Syncfusion.Blazor.TreeGrid;
using Syncfusion.Blazor.DropDowns;
using Microsoft.Graph.Models;
using TeyaWebApp.Services;
namespace TeyaWebApp.Components.Pages
{
    public partial class Appointments : ComponentBase
    {

        [Inject] private ActiveUser _ActiveUser { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        private Guid? OrgID { get; set; }
        private Guid selectedUserID { get; set; }
        private DateTime _selectedDate;
        private DateTime? Start_Time { get; set; }
        private DateTime? End_Time { get; set; }
        public DateTime Date_Value { get; set; }
        private DateTime currentTime { get; set; }
        private Guid selectedFacilityID { get; set; }
        private Guid selectedProviderID { get; set; }
        private bool isCardVisible { get; set; }
        private string selectedUserName { get; set; }
        private string selectedProvider { get; set; }
        private string filterProvider { get; set; }
        private string selectedVisitType { get; set; }
        private string selectedVisitStatus { get; set; }
        private string selectedReason { get; set; }
        private bool Subscription = false;
        private string selectedNotes { get; set; }
        private string selectroomNumber;
        private string val { get; set; }
        private string searchTerm { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        private string selectedCriteria { get; set; } = "Name"; // Default selection
        private string selectedFacility { get; set; } = string.Empty;
        private string filterFacility { get; set; } = string.Empty;

        private bool showNoResultsMessage = false;
        private bool showTimeError = false;
        private bool isEditMode = false;
        public enum UserRoles { Provider, Admin, Patient }

        private Guid currentAppointmentId = Guid.Empty;
        private MudDialog _AddAppointmentdialog;
        private SfSchedule<Appointment> ScheduleRef;
        private SfDropDownList<string, Member> userDropDown;
        private List<Appointment> appointments = new List<Appointment>();
        private List<Member> users = new List<Member>();
        private List<Member> ProviderListToFilter = new List<Member>();
        private List<Member> Provider_List = new List<Member>();
        private List<Member> filteredUsers = new List<Member>();
        private List<string> visitTypeOptions = new List<string>();
        private List<string> visitStatusOptions = new List<string>();
        private List<string> searchCriteriaOptions = new List<string> { "Name", "Phone Number", "SSN" };
        private string[] resourceNames = { UserRoles.Provider.ToString() };
        private string DialogTitle => isEditMode ? Localizer["EditAppointment"] : Localizer["AddAppointment"];
        private string[] selectedFacilities { get; set; } = Array.Empty<string>();
        private string[] selectedProviders { get; set; } = Array.Empty<string>();
        private List<Facility> FacilityList { get; set; } = new List<Facility>();
        private List<Member> FilteredProviders { get; set; } = new();
        private List<ProviderResource> CachedProviderResources { get; set; } = new List<ProviderResource>();
        private List<Guid> selectedFacilityIDs { get; set; } = new List<Guid>();
        private List<Guid> selectedProviderIDs { get; set; } = new List<Guid>();
        DateTime MinTime => DateTime.Today.AddHours(9);
        DateTime MaxTime => DateTime.Today.AddHours(17.5);

        /// <summary>
        /// modifying scheduling when date changed
        /// </summary>        
        private DateTime selectedDate
        {
            get => _selectedDate;
            set
            {
                if (_selectedDate != value)
                {
                    _selectedDate = value;
                    OnDateChanged(_selectedDate);
                    StateHasChanged();
                }
            }
        }

        /// <summary>
        /// Initializes the component by setting the default date values, 
        /// hiding the card initially, and loading users, appointments, 
        /// and various facility-related options.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            // Initialize collections first to avoid null reference exceptions
            appointments = new List<Appointment>();
            users = new List<Member>();
            FacilityList = new List<Facility>();
            filteredUsers = new List<Member>();
            visitTypeOptions = new List<string>();
            visitStatusOptions = new List<string>();
            try
            {
                var organizationId = await OrganizationService.GetOrganizationIdByNameAsync(_ActiveUser.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(organizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";
                var member = await MemberService.GetMemberByIdAsync(Guid.Parse(_ActiveUser.id),organizationId, Subscription);
                OrgID = member.OrganizationID;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting member by ID");
                OrgID = null;
            }

            selectedDate = DateTime.Today;
            Date_Value = DateTime.Today;
            isCardVisible = false;

            // Load data - these methods should handle their own exceptions
            await LoadUsers();
            await LoadFacilityOptions();
            await LoadVisitTypeOptions();
            await LoadVisitStatusOptions();
            await LoadAppointments();
            var filtered_Providers = users.Where(m => m.RoleName == UserRoles.Provider.ToString() && m.OrganizationID == OrgID).ToList();
            Provider_List = filtered_Providers;
            ProviderListToFilter = filtered_Providers.Prepend(new Member { UserName = Localizer["Select All"], Id = Guid.Empty, RoleName = UserRoles.Provider.ToString() }).ToList();
            CachedProviderResources = GetFilteredProviderResources();
        }

        /// <summary>
        /// Retrieves the filtered list of provider resources based on selected provider IDs.
        /// </summary>
        /// <returns>A filtered list of provider resources.</returns>
        private List<ProviderResource> GetFilteredProviderResources()
        {
            var allProviderResources = MapProvidersToResources(ProviderListToFilter);

            if (selectedProviderIDs == null ||
                selectedProviderIDs.Count == 0 ||
                selectedProviderIDs.Count == allProviderResources.Count)
            {
                return allProviderResources;
            }
            else
            {
                return allProviderResources.Where(provider => selectedProviderIDs.Contains(provider.Id)).ToList();
            }
        }

        /// <summary>
        /// Maps a list of provider members to a list of provider resources with assigned colors.
        /// </summary>
        /// <param name="providers">A collection of provider members.</param>
        /// <returns>A list of mapped provider resources.</returns>
        private List<ProviderResource> MapProvidersToResources(IEnumerable<Member> providers)
        {
            string[] colorPalette = new string[] { Localizer["#ffaa00"], Localizer["#f8a398"], Localizer["#7499e1"], Localizer["#cb6bb2"], Localizer["#56ca85"], Localizer["#33FFA1"] };
            var providerResources = new List<ProviderResource>();
            int index = 0;

            foreach (var provider in providers.Where(providerlist => providerlist.UserName != Localizer["Select All"]))
            {
                providerResources.Add(new ProviderResource
                {
                    Id = provider.Id,
                    UserName = provider.UserName,
                    Color = colorPalette[index % colorPalette.Length]
                });
                index++;
            }
            return providerResources;
        }

        private void OpenAddTaskDialog()
        {
            // Reset form for creating a new appointment
            ClearAppointmentForm();
            isEditMode = false;
            currentAppointmentId = Guid.Empty;
            _AddAppointmentdialog.ShowAsync();
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _AddAppointmentdialog.CloseAsync();
        }

        private async Task DeleteAppointment()
        {
            if (currentAppointmentId != Guid.Empty)
            {
                try
                {
                    await AppointmentService.DeleteAppointmentAsync(currentAppointmentId, OrgID, Subscription);
                    Snackbar.Add(Localizer["Appointment deleted successfully"], Severity.Success);
                    await LoadAppointments();
                    CloseAddTaskDialog();
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error deleting appointment");
                    Snackbar.Add(Localizer["Error deleting appointment"], Severity.Error);
                }
            }
        }

        /// <summary>
        /// Handles row selection in the grid.
        /// </summary>
        /// <param name="args">The selected row event arguments.</param>
        private void RowSelectHandler(RowSelectEventArgs<Member> args)
        {
            var selectedMember = args.Data;
            selectedUserName = selectedMember.UserName;
            selectedUserID = selectedMember.Id;
            filteredUsers = new List<Member>();
            searchTerm = string.Empty;

            // Update the UI
            StateHasChanged();
        }

        /// <summary>
        /// Handles when a user clicks on an event in the scheduler
        /// </summary>
        private async Task OnEventSelect(EventClickArgs<Appointment> args)
        {
            args.Cancel = true;
            if (args.Event != null)
            {
                var appointment = args.Event;
                // Set up the data for the edit dialog
                selectedUserName = appointment.PatientName;
                selectedUserID = appointment.PatientId;
                selectedFacility = appointment.Facility;
                selectedFacilityID = appointment.FacilityId;
                selectedProvider = appointment.Provider;
                selectedProviderID = appointment.ProviderId;
                selectedVisitType = appointment.VisitType;
                selectedVisitStatus = appointment.VisitStatus;
                selectedReason = appointment.Reason;
                selectedNotes = appointment.Notes;
                selectroomNumber = appointment.RoomNumber;
                Start_Time = appointment.StartTime;
                End_Time = appointment.EndTime;
                Date_Value = appointment.AppointmentDate;

                // Open the dialog for editing
                isEditMode = true;
                currentAppointmentId = appointment.Id;
                await InvokeAsync(() => _AddAppointmentdialog.ShowAsync());
            }
        }

        /// <summary>
        /// Adds a new appointment with the specified details. 
        /// Validates the time range before creating the appointment.
        /// Updates the schedule and removes the cached data upon successful creation.
        /// Logs an error if the operation fails.
        /// </summary>
        private async Task AddAppointment()
        {
            if (string.IsNullOrEmpty(selectedFacility) || string.IsNullOrEmpty(selectedUserName) ||
                string.IsNullOrEmpty(selectedProvider) || string.IsNullOrEmpty(selectedVisitType) ||
                string.IsNullOrEmpty(selectedVisitStatus) || string.IsNullOrEmpty(selectedReason) ||
                string.IsNullOrEmpty(selectedNotes) || string.IsNullOrEmpty(selectroomNumber))
            {
                Snackbar.Add(@Localizer["Please fill in All Fields"], Severity.Warning);
                return;
            }

            if (Start_Time == null || End_Time == null)
            {
                Snackbar.Add(@Localizer["Please select start and end times"], Severity.Warning);
                return;
            }

            DateTime appointmentStart = new DateTime(Date_Value.Year, Date_Value.Month, Date_Value.Day,
                                                Start_Time.Value.Hour, Start_Time.Value.Minute, 0);

            // Check if the appointment is in the past
            if (appointmentStart < DateTime.Now)
            {
                Snackbar.Add(@Localizer["Invalid Appointment Date or Time"], Severity.Warning);
                return;
            }

            if (End_Time <= Start_Time)
            {
                showTimeError = true;
                Snackbar.Add(@Localizer["End time must be after start time"], Severity.Warning);
                return;
            }
            else
            {
                showTimeError = false;

                try
                {
                    if (isEditMode && currentAppointmentId != Guid.Empty)
                    {
                        // Update existing appointment
                        var updatedAppointment = new Appointment
                        {
                            Id = currentAppointmentId,
                            UserId = Guid.Parse(_ActiveUser.id),
                            ProviderId = selectedProviderID,
                            PatientId = selectedUserID,
                            FacilityId = selectedFacilityID,
                            OrganisationId = OrgID,
                            StartTime = new DateTime(Date_Value.Year, Date_Value.Month, Date_Value.Day, Start_Time.Value.Hour, Start_Time.Value.Minute, Start_Time.Value.Second),
                            EndTime = new DateTime(Date_Value.Year, Date_Value.Month, Date_Value.Day, End_Time.Value.Hour, End_Time.Value.Minute, End_Time.Value.Second),
                            AppointmentDate = Date_Value,
                            CreatedDate = DateTime.Now, // You might want to preserve the original creation date
                            UpdatedDate = DateTime.Now,
                            Subscription = false,
                            Facility = selectedFacility,
                            PatientName = selectedUserName,
                            Provider = selectedProvider,
                            VisitType = selectedVisitType,
                            VisitStatus = selectedVisitStatus,
                            Reason = selectedReason,
                            Notes = selectedNotes,
                            RoomNumber = selectroomNumber
                        };

                        await AppointmentService.UpdateAppointmentAsync(updatedAppointment);
                        Snackbar.Add(@Localizer["Appointment updated successfully"], Severity.Success);
                    }
                    else
                    {
                        // Create new appointment
                        var newAppointments = new List<Appointment>();
                        var appointment = new Appointment
                        {
                            Id = Guid.NewGuid(),
                            UserId = Guid.Parse(_ActiveUser.id),
                            ProviderId = selectedProviderID,
                            PatientId = selectedUserID,
                            FacilityId = selectedFacilityID,
                            OrganisationId = OrgID,
                            StartTime = new DateTime(Date_Value.Year, Date_Value.Month, Date_Value.Day, Start_Time.Value.Hour, Start_Time.Value.Minute, Start_Time.Value.Second),
                            EndTime = new DateTime(Date_Value.Year, Date_Value.Month, Date_Value.Day, End_Time.Value.Hour, End_Time.Value.Minute, End_Time.Value.Second),
                            AppointmentDate = Date_Value,
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now,
                            Subscription = false,
                            Facility = selectedFacility,
                            PatientName = selectedUserName,
                            Provider = selectedProvider,
                            VisitType = selectedVisitType,
                            VisitStatus = selectedVisitStatus,
                            Reason = selectedReason,
                            Notes = selectedNotes,
                            RoomNumber = selectroomNumber
                        };
                        newAppointments.Add(appointment);
                        await AppointmentService.CreateAppointmentsAsync(newAppointments);
                        Snackbar.Add(@Localizer["Appointment created successfully"], Severity.Success);
                    }

                    await LoadAppointments();
                    CloseAddTaskDialog();

                    // Clear form fields after successful submission
                    ClearAppointmentForm();

                    // Reset edit mode
                    isEditMode = false;
                    currentAppointmentId = Guid.Empty;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, Localizer["Error"]);
                    Snackbar.Add(@Localizer["Error processing appointment"], Severity.Error);
                }
            }
        }

        /// <summary>
        /// Clears the appointment form fields after submission
        /// </summary>
        private void ClearAppointmentForm()
        {
            selectedUserName = null;
            selectedUserID = Guid.Empty;
            selectedFacility = string.Empty;
            selectedProvider = string.Empty;
            selectedVisitType = string.Empty;
            selectedVisitStatus = string.Empty;
            selectedReason = string.Empty;
            selectedNotes = string.Empty;
            selectroomNumber = string.Empty;
            Start_Time = null;
            End_Time = null;
            Date_Value = DateTime.Today;
            searchTerm = string.Empty;
            filteredUsers = new List<Member>();
        }

        private async Task OnFacilityChanged(string newFacility)
        {
            selectedFacility = newFacility;  // Update the selectedFacility property
            if (string.IsNullOrEmpty(newFacility))
            {
                selectedFacilityID = Guid.Empty; // No facility selected
            }
            else
            {
                selectedFacilityID = FacilityList.FirstOrDefault(x => x.FacilityName == newFacility)?.FacilityId ?? Guid.Empty;
            }

        }

        private async Task OnProviderChanged(string newProvider)
        {
            selectedProvider = newProvider; // Update the selectedProvider property

            if (string.IsNullOrEmpty(newProvider))
            {
                selectedProviderID = Guid.Empty; // No provider selected
            }
            else
            {
                selectedProviderID = ProviderListToFilter.FirstOrDefault(provider =>
                   provider.UserName == newProvider &&
                   provider.RoleName == UserRoles.Provider.ToString())?.Id ?? Guid.Empty;
            }

        }

        /// <summary>
        /// Handles the change in selected date and reloads appointments accordingly.
        /// </summary>
        private async Task OnDateChanged(DateTime date)
        {
            selectedDate = date;
            await LoadAppointments();
            StateHasChanged();
        }

        /// <summary>
        /// Searches for users based on the selected criteria (SSN, Name, or PhoneNumber) 
        /// and updates the filtered user list accordingly.
        /// </summary>
        private async Task SearchUsers()
        {
            filteredUsers = new List<Member>(); // Initialize to empty list

            var patientUsers = users.Where(u => u.RoleName == Localizer["Patient"]);

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                filteredUsers = patientUsers.ToList();
            }
            else
            {
                switch (selectedCriteria)
                {
                    case "SSN":
                        filteredUsers = patientUsers.Where(u => u.SSN != null && u.SSN.Contains(searchTerm)).ToList();
                        break;
                    case "Name":
                        filteredUsers = patientUsers.Where(u => u.UserName != null && u.UserName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
                        break;
                    case "Phone Number":
                        filteredUsers = patientUsers.Where(u => u.PhoneNumber != null && u.PhoneNumber.Contains(searchTerm)).ToList();
                        break;
                    default:
                        filteredUsers = patientUsers.ToList();
                        break;
                }
            }
            showNoResultsMessage = filteredUsers.Count == 0;
            StateHasChanged();
        }

        /// <summary>
        /// Loads appointment data and updates the schedule view.
        /// </summary>
        private async Task LoadAppointments()
        {
            try
            {
                appointments = new List<Appointment>();
                var fetchedAppointments = await AppointmentService.GetAppointmentsAsync(selectedDate, OrgID, Subscription);
                if (selectedFacilityIDs.Count > 0)
                {
                    fetchedAppointments = fetchedAppointments
                        .Where(a => selectedFacilityIDs.Contains(a.FacilityId))
                        .ToList();
                }
                if (selectedProviderIDs.Count > 0)
                {
                    fetchedAppointments = fetchedAppointments
                        .Where(a => selectedProviderIDs.Contains(a.ProviderId))
                        .ToList();
                }
                if (fetchedAppointments != null)
                {
                    appointments = fetchedAppointments;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
                appointments = new List<Appointment>();
            }

            // Force component update
            StateHasChanged();
            // Refresh the scheduler component
            if (ScheduleRef != null)
            {
                try
                {
                    await ScheduleRef.RefreshAsync();
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error refreshing scheduler");
                }
            }
        }
        /// <summary>
        /// Loads the list of users from the MemberService.
        /// Logs an error if the operation fails.
        /// </summary>
        private async Task LoadUsers()
        {
            try
            {
                var allUsers = await MemberService.GetAllMembersAsync(OrgID.Value, Subscription);
                if (allUsers != null)
                {
                    users = allUsers;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
                users = new List<Member>();
            }
            StateHasChanged();
        }

        /// <summary>
        /// Loads the available facility options from the FacilityService.
        /// Logs an error if the operation fails.
        /// </summary>
        private async Task LoadFacilityOptions()
        {
            try
            {
                var facilities = await FacilityService.GetAllFacilitiesByOrgIdAsync(OrgID, Subscription);
                if (facilities != null)
                {
                    FacilityList = facilities;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
                FacilityList = new List<Facility>();
            }
        }

        /// <summary>
        /// Loads the available visit type options from the VisitTypeService.
        /// Logs an error if the operation fails.
        /// </summary>
        private async Task LoadVisitTypeOptions()
        {
            try
            {
                var types = await VisitTypeService.GetVisitTypeNamesAsync(OrgID);
                if (types != null)
                {
                    visitTypeOptions = types;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
                visitTypeOptions = new List<string>();
            }
        }

        /// <summary>
        /// Loads the available visit status options from the VisitStatusService.
        /// Logs an error if the operation fails.
        /// </summary>
        private async Task LoadVisitStatusOptions()
        {
            try
            {
                var statuses = await VisitStatusService.GetVisitStatus_StatusAsync();
                if (statuses != null)
                {
                    visitStatusOptions = statuses;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
                visitStatusOptions = new List<string>();
            }
        }

        /// <summary>
        /// Determines and returns the current status of an appointment based on the current time.
        /// </summary>
        private string GetAppointmentStatus(Appointment appointment)
        {
            currentTime = DateTime.Now;
            if (currentTime > appointment.EndTime)
            {
                val = Localizer["Completed"];
            }
            else if (currentTime >= appointment.StartTime && currentTime <= appointment.EndTime)
            {
                val = Localizer["Ongoing"];
            }
            else
            {
                val = Localizer["Scheduled"];
            }
            return val;
        }

        /// <summary>
        /// Filters the provider list based on the selected providers and updates the selected provider IDs.
        /// </summary>
        /// <param name="newProviders">An array of selected provider usernames.</param>
        private async Task OnProvidersFilter(string[] newProviders)
        {
            if (newProviders != null && newProviders.Contains(Localizer["Select All"]))
            {
                selectedProviders = ProviderListToFilter
                    .Where(providers => providers.UserName != Localizer["Select All"])
                    .Select(providers => providers.UserName)
                    .ToArray();

                selectedProviderIDs.Clear();
                foreach (var provider in ProviderListToFilter.Where(p => p.UserName != Localizer["Select All"]))
                {
                    if (provider.Id != Guid.Empty)
                    {
                        selectedProviderIDs.Add(provider.Id);
                    }
                }
            }
            else
            {
                selectedProviders = newProviders;
                selectedProviderIDs.Clear();
                if (newProviders != null && newProviders.Length > 0)
                {
                    foreach (var provider in newProviders)
                    {
                        var providerId = ProviderListToFilter.FirstOrDefault(providers =>
                            providers.UserName == provider &&
                            providers.RoleName == UserRoles.Provider.ToString())?.Id ?? Guid.Empty;
                        if (providerId != Guid.Empty)
                        {
                            selectedProviderIDs.Add(providerId);
                        }
                    }
                }
            }
            CachedProviderResources = GetFilteredProviderResources();
            await LoadAppointments();
            if (ScheduleRef != null)
            {
                await ScheduleRef.RefreshAsync();
            }
        }

        /// <summary>
        /// Filters the facility list based on the selected facilities and updates the selected facility IDs.
        /// </summary>
        /// <param name="newFacilities">An array of selected facility names.</param>
        private async Task OnFacilitiesFilter(string[] newFacilities)
        {
            selectedFacilities = newFacilities;
            selectedFacilityIDs.Clear();

            if (newFacilities != null && newFacilities.Length > 0)
            {
                foreach (var facility in newFacilities)
                {
                    var facilityId = FacilityList.FirstOrDefault(facilities => facilities.FacilityName == facility)?.FacilityId ?? Guid.Empty;
                    if (facilityId != Guid.Empty)
                    {
                        selectedFacilityIDs.Add(facilityId);
                    }
                }
            }
            await LoadAppointments();
        }

    }
}