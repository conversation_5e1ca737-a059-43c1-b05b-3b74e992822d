﻿@inherits LayoutComponentBase
@using MudBlazor
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.ViewModel
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager NavigationManager
@inject ITokenService TokenService
@using TeyaAIScribeResource
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<MudDialog Class="blur dialog-background" TitleClass="blur dialog-background-title" ContentClass="dialog-background-surface py-4" ActionsClass="dialog-background-surface" Style="width: 400px; height: 400px; border-radius:20px; position: fixed; top: 80px; right: 20px;">
    <TitleContent>
        <div class="dialog-title">
            <span class="dialog-title-text"><h5>@Localizer["User profile"]</h5></span>
        </div>
    </TitleContent>
    <DialogContent>
        <div class="content-center">
            <div class="profile-circle">
                <span class="profile-letter">@GetInitials(StateContainer.ExtractedName)</span>
            </div>
            <MudText Class="centered-text">Hi, @StateContainer.Username!</MudText>
            <MudButton Class="manage-button" OnClick="NavigateToManageProfile">@Localizer["ManagYourTeyaAccount"]</MudButton>
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton Color="Color.Error" OnClick="signout">@Localizer["SignOut"]</MudButton>
        <MudButton Color="Color.Primary" OnClick="Submit">@Localizer["Ok"]</MudButton>
    </DialogActions>
</MudDialog>

<style>
    .mud-overlay-scrim {
        display: none !important; 
    }

    .blur {
        backdrop-filter: blur(10px);
    }

    .dialog-background {
        background-color: rgb(233, 238, 246);
    }

    .dialog-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .dialog-title-text {
        flex-grow: 1;
        text-align: center;
        font-size: 20px;
    }

    .content-center {
        display: flex;
        padding:4%;
        flex-direction: column;
        align-items: center;
    }

    .centered-text {
        text-align: center;
        font-size: 22px;
        margin: 2%;
    }

    .profile-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background-color: #007BFF;
        display: flex;
        margin:2%;
        justify-content: center;
        align-items: center;
        font-size: 40px; 
        color: white; 
    }

    .manage-button {
        margin-top: 8px; 
        background-color: rgb(233, 238, 246);
        color: #007BFF;
        border: 0.9px solid black;
        border-radius: 50px;
        width: 250px; 
        height: 50px; 
        text-transform: none; 
        transition: 0.3s;
        cursor: pointer; 
    }
     .manage-button:hover {
            background-color: #007BFF; 
            color: white; 
            border-color: #0056b3; 
        }
</style>

