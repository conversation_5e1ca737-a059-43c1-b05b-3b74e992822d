﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaWebApp.Services
{
    public interface IPageRoleMappingService
    {
        Task<IEnumerable<string>> GetRolesByPagePathAsync(string pagePath);
        Task<IEnumerable<PageRoleMappingData>> GetPagesByRoleIdAsync(Guid roleId);
        Task<IEnumerable<PageRoleMappingData>> GetPageRoleMappingsAsync();
        Task<PageRoleMappingData> GetPageRoleMappingByIdAsync(Guid id);
        Task AddPageRoleMappingAsync(PageRoleMappingData pageRoleMapping);
        Task UpdatePageRoleMappingAsync(PageRoleMappingData pageRoleMapping);
        Task DeletePageRoleMappingByIdAsync(Guid id);
    }
}
