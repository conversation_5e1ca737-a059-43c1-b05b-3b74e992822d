﻿using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;

namespace TeyaWebApp.Components.Pages
{
    public partial class DXAlert
    {
        [Inject] public IDXAlertService DXAlertService { get; set; }
        [Inject] private ILogger<DiagnosisAlert> _logger { get; set; }
        [Inject] private IStringLocalizer<DiagnosisAlert> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }

        [CascadingParameter] private MudDialogInstance? MudDialog { get; set; }

        private SfGrid<DiagnosisAlert>? DiagnosisAlertGrid;

        private List<DiagnosisAlert> dxAlerts = new();
        private List<DiagnosisAlert> deleteAlertList = new();
        private List<DiagnosisAlert> addList = new();

        // Form fields
        private string alertName = string.Empty;
        private string alertDescription = string.Empty;
        private string webReference = string.Empty;
        private int? ageLowerBound;
        private int? ageUpperBound;
        private string orderSet = string.Empty;
        private string gender = "Both";

        [Inject]
        private PatientService PatientService { get; set; } = default!;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }

        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientService.PatientData.Id;
            OrgID = PatientService.PatientData.OrganizationID;
            try
            {
                await LoadAlertsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dx alert data");
            }
        }
        private async Task LoadAlertsAsync()
        {
            var existingAlerts = await DXAlertService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, false);
            dxAlerts = existingAlerts?.ToList() ?? new List<DiagnosisAlert>();
            int emptyRowsNeeded = 9 - dxAlerts.Count;
            if (emptyRowsNeeded > 0)
            {
                dxAlerts.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                    .Select(_ => new DiagnosisAlert
                    {
                        Name = string.Empty,
                        Description = string.Empty,
                        WebReference = string.Empty,
                        OrderSet = string.Empty,
                        Gender = string.Empty
                    }));
            }
        }
        public void ActionCompletedHandler(ActionEventArgs<DiagnosisAlert> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedAlert = args.Data;
                var existingItem = addList.FirstOrDefault(v => v.Id == deletedAlert.Id);

                if (existingItem != null)
                {
                    addList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;  // Set IsActive to false instead of actually deleting
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteAlertList.Add(args.Data);
                }
            }
        }
        public async Task ActionBeginHandler(ActionEventArgs<DiagnosisAlert> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }
        private async Task SaveData()
        {
            try
            {
                if (addList.Count > 0)
                {
                    // Set IsActive to true for all new alerts
                    foreach (var alert in addList)
                    {
                        alert.IsActive = true;
                    }
                    await DXAlertService.AddDXAlertsAsync(addList, OrgID, false);
                }

                if (deleteAlertList.Count > 0)
                {
                    await DXAlertService.UpdateDXAlertsListAsync(deleteAlertList, OrgID, false);
                }
                var existingAlerts = dxAlerts.Where(a => !string.IsNullOrEmpty(a.Name) && a.Id != Guid.Empty).ToList();
                if (existingAlerts.Count > 0)
                {
                    await DXAlertService.UpdateDXAlertsListAsync(existingAlerts, OrgID, false);
                }
                deleteAlertList.Clear();
                addList.Clear();

                await LoadAlertsAsync();
                ResetInputFields();

                Snackbar.Add(_localizer["DX alerts saved successfully"], Severity.Success);
                MudDialog?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving dx alert data");
                Snackbar.Add(_localizer["Failed to save dx alert records"], Severity.Error);
            }
        }
        private async Task CancelData()
        {
            deleteAlertList.Clear();
            addList.Clear();
            await LoadAlertsAsync();
            ResetInputFields();

            // Close the dialog
            MudDialog?.Close();
        }
        private void ResetInputFields()
        {
            alertName = string.Empty;
            alertDescription = string.Empty;
            webReference = string.Empty;
            ageLowerBound = null;
            ageUpperBound = null;
            orderSet = string.Empty;
            gender = "Both";
        }

        private async Task AddNewAlert()
        {
            try
            {
                if (string.IsNullOrEmpty(alertName))
                {
                    Snackbar.Add(_localizer["Please enter alert name"], Severity.Warning);
                    return;
                }

                var emptyRow = dxAlerts.FirstOrDefault(i => string.IsNullOrEmpty(i.Name));

                if (emptyRow == null)
                {
                    emptyRow = new DiagnosisAlert();
                    dxAlerts.Add(emptyRow);
                }

                emptyRow.Id = Guid.NewGuid();
                emptyRow.PatientId = PatientId;
                emptyRow.pcpId = Guid.Parse(User.id);
                emptyRow.OrganizationId = OrgID ?? Guid.Empty;
                emptyRow.CreatedDate = DateTime.Now;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.Name = alertName;
                emptyRow.Description = alertDescription;
                emptyRow.WebReference = webReference;
                emptyRow.AgeLowerBound = ageLowerBound;
                emptyRow.AgeUpperBound = ageUpperBound;
                emptyRow.OrderSet = orderSet;
                emptyRow.Gender = gender;
                emptyRow.IsActive = true;  // Set IsActive to true for new alerts

                addList.Add(emptyRow);

                if (DiagnosisAlertGrid != null)
                {
                    await DiagnosisAlertGrid.Refresh();
                }

                ResetInputFields();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new dx alert");
                Snackbar.Add(_localizer["Failed to add dx alert"], Severity.Error);
            }
        }
    }
}
