﻿using System;
using System.Collections.ObjectModel;
using Microsoft.Maui.Controls;
using Syncfusion.Maui.Scheduler;
using Microsoft.Maui.Graphics;
using System.Dynamic;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;
using System.Security.Cryptography;
using Microsoft.AspNetCore.Components;
using TeyaMobile;
namespace TeyaMobile;

public partial class AppointmentsDoctor : ContentPage
{
    private readonly ISpeechService _speechService;
    private readonly PatientService _patientService;
    private readonly NavigationManager _navigationManager;
    private bool _isNavigating = false;

    public AppointmentsDoctor(
        AppointmentService appointmentService,
        PatientService patientService,
        NavigationManager navigationManager,
        IServiceProvider serviceProvider,
        ISpeechService speechService)
    {
        InitializeComponent();
        BindingContext = new AppointmentsViewModel(appointmentService);

        _patientService = patientService;
        _navigationManager = navigationManager;
        _speechService = speechService;
        doctorScheduler.Tapped += OnSchedulerTapped;
    }

    private async void OnSchedulerTapped(object? sender, SchedulerTappedEventArgs e)
    {
        if (_isNavigating) return;
        _isNavigating = true;

        try
        {
            if (e.Appointments != null && e.Appointments.Count > 0)
            {
                var rawAppointment = e.Appointments[0];

                if (rawAppointment is Appointment selectedAppointment)
                {
                    _patientService.SelectedPatient = selectedAppointment;

                    if (Shell.Current.CurrentState.Location.OriginalString != "TeyaAI")
                    {
                        await Shell.Current.GoToAsync("TeyaAI");
                    }
                }
                else
                {
                    await DisplayAlert("Error", "Unexpected appointment type.", "OK");
                }
            }
        }
        finally
        {
            _isNavigating = false;
        }
    }
}
