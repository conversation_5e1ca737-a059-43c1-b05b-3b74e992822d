﻿using System;
using System.Text.Json.Serialization;
using TeyaMobileModel.Model;

namespace TeyaMobileModel.Model
{
    public class FamilyMember : IModel
    {
        public Guid RecordID { get; set; } 
        public Guid? OrganizationID { get; set; }
        public Guid PatientId { get; set; }
        public string Relation { get; set; }
        public string Status { get; set; }
        public DateTime? DOB { get; set; } 
        public int? Age { get; set; } 
        public string Notes { get; set; }
        public bool IsActive { get; set; }

    }
}