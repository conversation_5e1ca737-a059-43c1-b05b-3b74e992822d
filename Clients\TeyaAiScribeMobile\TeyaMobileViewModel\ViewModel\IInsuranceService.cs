﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IInsuranceService
    {
        Task<Insurance> GetInsuranceByIdAsync(Guid id);
        Task<List<Insurance>> GetAllInsurancesAsync();
        Task<bool> AddInsuranceAsync(Insurance insurance);
        Task<bool> UpdateInsuranceAsync(Guid id, Insurance insurance);
        Task<bool> DeleteInsuranceAsync(Guid id);
    }
}
