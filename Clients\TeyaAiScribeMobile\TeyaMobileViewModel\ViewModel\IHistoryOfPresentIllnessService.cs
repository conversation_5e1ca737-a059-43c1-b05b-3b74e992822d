﻿
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IHistoryOfPresentIllnessService
    {
        Task<List<HistoryOfPresentIllness>> GetHpiByPatientIdAsync(Guid patientId);
        Task<List<HistoryOfPresentIllness>> GetActiveHpiByPatientIdAsync(Guid patientId);
        Task<HistoryOfPresentIllness> GetHpiByIdAsync(Guid id);
        Task AddHpiAsync(List<HistoryOfPresentIllness> hpiRecords);
        Task DeleteHpiAsync(Guid hpiId);
        Task UpdateHpiAsync(HistoryOfPresentIllness hpiRecord);
        Task UpdateHpiListAsync(List<HistoryOfPresentIllness> hpiRecords);

    }
}
