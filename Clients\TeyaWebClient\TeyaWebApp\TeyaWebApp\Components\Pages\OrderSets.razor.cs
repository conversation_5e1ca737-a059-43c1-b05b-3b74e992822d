﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.ComponentModel.DataAnnotations;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using static MudBlazor.Icons.Custom;
using Microsoft.Extensions.Localization;
using TeyaWebApp.Services;
using TeyaUIModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class OrderSets : ComponentBase
    {
        private MudDialog _addMemberDialog;
        private SfGrid<DiagnosticImage> todayImagingGrid { get; set; }
        private List<string> ToolbarItems = new List<string> { "Add" };
        private SfGrid<OrderSetDiagnosticImaging> futureImagingGrid { get; set; }
        public string drugName { get; set; }
        private FDBRoutedMedication selectedRoutedMedication;
        private List<OrderSetActiveMedication> medications = new();
        private List<string> chiefComplaintDescriptions = new List<string>();
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientID { get; set; }
        private string _Quantity;
        private FDBMedicationName selectedMedication;
        private string _Frequency;
        [Inject] public IRxNormService RxNormService { get; set; }
        public SfGrid<OrderSetActiveMedication> MedicinesGrid { get; set; }
        protected List<string> BrandNames { get; set; } = new List<string>();
        [Inject] public ICurrentMedicationService CurrentMedicationService { get; set; }
        [Inject]
        private IChiefComplaintService ChiefComplaintService { get; set; }
        private List<RxNormConcept> RxMedication { get; set; } = new();

        private List<ChiefComplaintDTO> chiefComplaints = new();
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        public enum Source { FDB, RxNorm }
        string OrderSetName;
        string DiagnosisLinked;
        public List<string> BrandSBD { get; set; } = new List<string>();
        private string selectedDatabase = Source.RxNorm.ToString();

        [Inject] private SharedNotesService SharedNotesService { get; set; }
        public string finalSBD { get; set; }
        private bool newOrderSet = true;
        private bool showRXForm = false;
        private bool showDiagnosticImagingForm = false;
        private bool showImmunizationForm = false;
        private bool showLabsForm = false;
        private bool showSelected = false;
        private bool showProcedureForm = false;
        private Guid? OrgID { get; set; }
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        [Inject] public IVaccineService VaccineService { get; set; }
        public string VaccineName { get; set; }
        private SfGrid<OrderSetImmunizationData> ImmunizationGrid;
        [Inject] public IFDBService FDBService { get; set; }
        public string SelectedCPTCode { get; set; }
        public string SelectedCVXCode { get; set; }
        public string SelectedCPTDescription { get; set; }
        public DateTime GivenDate { get; set; } = DateTime.Now;
        public string Comments { get; set; }
        private string selectedOrderSet;
        private bool showNoResultsMessage;
        private bool updateOrderset = false;
        private bool deleteOrderset = false;
        private Guid organizationId;
        private List<FDBRoutedMedication> RoutedMedications { get; set; } = new();
        [Inject] private ILogger<Immunization> _logger { get; set; }
        private List<Vaccines> _details { get; set; } = new List<Vaccines>();
        private List<CompleteOrderSet> CompleteOrderSetList { get; set; } = new List<CompleteOrderSet>();
        private CompleteOrderSet FilteredOrderSetList { get; set; } = new CompleteOrderSet();
        [Inject] private IStringLocalizer<Immunization> _localizer { get; set; }
        private List<OrderSetImmunizationData> immunization = new();
        private SfGrid<OrderSetProcedures> ProcedureGrid;
        private List<OrderSetProcedures> procedure = new();
        private CPT selectedCPT;
        private List<FDBMedicationName> FDBmedications { get; set; } = new();
        private List<AssessmentsData> LocalAssessmentsData = new();
        private List<CPT> _cptCodes { get; set; } = new List<CPT>();
        private List<string> AssessmentDiagnosis = new List<string>();
        [Inject] IOrganizationService organizationService { get; set; }
        [Inject] IAssessmentsService assessmentsService { get; set; }
        [Inject] IOrderSetService orderSetService { get; set; }
        [Inject] public ICPTService _CPTService { get; set; }
        public SfGrid<OrderSetLabTests> PlanLabsGrid { get; set; }
        private List<OrderSetLabTests> planlabs { get; set; } = new List<OrderSetLabTests>();
        private OrderSetDiagnosticImaging newDiagnosticImaging = new();
        private List<OrderSetDiagnosticImaging> DiagnosticImagingList = new();
        private List<DiagnosticImage> FutureImagingList { get; set; }
        private List<FDBRoutedDosageFormMedication> RoutedDosageFormMedications { get; set; } = new();
        private FDBRoutedDosageFormMedication selectedRoutedDosageFormMedication;

        private List<FDBMedication> FinalMedications { get; set; } = new();
        private FDBMedication selectedFinalMedication;

        private List<FDBRouteLookUp> FDBRouteLookUps { get; set; }
        private List<FDBTakeLookUp> FDBTakeLookUps { get; set; }

        private Dictionary<string, string> routeNameLookup;
        private Dictionary<string, string> takeNameLookup;

        /// <summary>
        /// get list of Order Set Record from database
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                
                CompleteOrderSetList = (await orderSetService.GetAllOrderSetAsync()).ToList();
                SharedNotesService.OnChange += UpdateComplaints;
                _details = await VaccineService.GetAllVaccinesDataAsync();
                SharedNotesService.OnChange += UpdateAssessments;
                
                organizationId = await organizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                _cptCodes = await _CPTService.GetAllCPTCodesAsync();
                chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();

                FDBmedications = await FDBService.GetAllFDBMedications();
                RxMedication = await RxNormService.GetAllRxNormMedications();
                BrandNames = RxMedication.Select(m => m.STR).Distinct().ToList();
                FDBRouteLookUps = await FDBService.GetFDBRouteLookUp();
                FDBTakeLookUps = await FDBService.GetFDBTakeLookUp();
                routeNameLookup = FDBRouteLookUps.ToDictionary(x => x.MED_ROUTE_ID, x => x.Route_Name);
                takeNameLookup = FDBTakeLookUps.ToDictionary(x => x.MED_DOSAGE_FORM_ID, x => x.Take_Name);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        private void OnCompleteOrderSetClicked(CompleteOrderSet selected)
        {
            selectedOrderSet = selected.orderSet.OrderSetName;
            OrderSetName = selected.orderSet.OrderSetName;
            DiagnosisLinked = selected.orderSet.Diagnosis;
            planlabs = selected.orderSetLabTests;
            procedure = selected.orderSetProcedures;
            immunization = selected.orderSetImmunizationData;
            medications = selected.orderSetActiveMedication;
            DiagnosticImagingList = new List<OrderSetDiagnosticImaging> { selected.orderSetDiagnosticImaging };
            showSelected = true;
            updateOrderset = false;
            newOrderSet = false;
            StateHasChanged();
        }

        //When Medication name is selected
        private async Task OnMedicationSelected(FDBMedicationName med)
        {
            selectedMedication = med;
            selectedRoutedMedication = null;
            RoutedMedications.Clear();
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.MED_NAME_ID))
            {
                RoutedMedications = await FDBService.GetFDBRoutedMedications(med.MED_NAME_ID);
            }
        }

        private async Task<IEnumerable<string>> SearchOrderSet(string searchTerm, CancellationToken cancellationToken)
        {
            if (searchTerm == null || string.IsNullOrWhiteSpace(searchTerm))
            {
                selectedOrderSet = null;
                showNoResultsMessage = false;
                StateHasChanged();
                return new List<string> { };
            }
            else
            {
                var filtered = CompleteOrderSetList
                            .Where(temp => temp.orderSet != null && temp.orderSet.OrderSetName != null &&
                                       temp.orderSet.OrderSetName.Contains(searchTerm, StringComparison.InvariantCultureIgnoreCase))
                            .Select(temp => temp.orderSet.OrderSetName)
                            .ToList();

                if (!filtered.Any())
                {
                    showNoResultsMessage = true;
                    return new List<string> { "No OrderSet Found" };
                }

                showNoResultsMessage = false;
                return filtered;
            }
        }
        private void OnOrderSetSelected(string orderSet)
        {
            selectedOrderSet = orderSet;
            deleteOrderset = false;
            updateOrderset = false;
            FilterOrderSet();
            StateHasChanged();
        }

        public void ResetInputFields()
        {
            OrderSetName = string.Empty;
            DiagnosisLinked = string.Empty;
            SelectedCPTCode = string.Empty;
            SelectedCVXCode = string.Empty;
            VaccineName = string.Empty;
            Comments = string.Empty;
            selectedCPT = new CPT();
            SelectedCPTDescription = string.Empty;
            planlabs = new List<OrderSetLabTests>();
            medications = new List<OrderSetActiveMedication>();
            immunization = new List<OrderSetImmunizationData>();
            procedure = new List<OrderSetProcedures>();
            newDiagnosticImaging = new OrderSetDiagnosticImaging();
            DiagnosticImagingList = new List<OrderSetDiagnosticImaging>();
        }
        public void AddNewOrderSet()
        {
            showRXForm = false;
            showDiagnosticImagingForm = false;
            showImmunizationForm = false;
            showLabsForm = false;
            showProcedureForm = false;

            ResetInputFields();

            StateHasChanged();

            newOrderSet = true;
            deleteOrderset = false;
            updateOrderset = false;
            showSelected = false;
        }
        public void DeleteOrderSet()
        {
            newOrderSet = false;
            updateOrderset = false;
            showSelected = true;
            deleteOrderset = true;
        }

        public void UpdateOrderSet()
        {
            newOrderSet = false;
            deleteOrderset = false;
            updateOrderset = true;
            showSelected = true;
        }
        private async void FilterOrderSet()
        {
            if (selectedOrderSet == null || string.IsNullOrWhiteSpace(selectedOrderSet))
            {
                FilteredOrderSetList = CompleteOrderSetList.FirstOrDefault();
            }
            else
            {
                showSelected = true;
                newOrderSet = false;
                string[] parts = selectedOrderSet.Split(' ');
                FilteredOrderSetList = CompleteOrderSetList
                                       .FirstOrDefault(temp => temp.orderSet?.OrderSetName == selectedOrderSet);

                planlabs = FilteredOrderSetList.orderSetLabTests;
                procedure = FilteredOrderSetList.orderSetProcedures;
                immunization = FilteredOrderSetList.orderSetImmunizationData;
                medications = FilteredOrderSetList.orderSetActiveMedication;
                DiagnosticImagingList = new List<OrderSetDiagnosticImaging> { FilteredOrderSetList.orderSetDiagnosticImaging };
            }

            await InvokeAsync(StateHasChanged);
            StateHasChanged();
        }
        private List<string> OrganizationOptions => new List<string>
        {
                Localizer["Quest Inc"].Value,
                Localizer["Lab Corp"].Value
        };

        private RenderFragment<object> OrganizationEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests labTest) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", OrganizationOptions);
            builder.AddAttribute(2, "Value", labTest.TestOrganization);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    labTest.TestOrganization = value;
                }));
            builder.AddAttribute(4, "Placeholder", "Select Organization");
            builder.CloseComponent();
        };
        private void UpdateAssessments()
        {
            
            OnInitializedAsync();
            StateHasChanged();  
        }
        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not Procedures process) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", process.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    process.AssessmentData = value;
                    var selectedAssessment = LocalAssessmentsData.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        //medication.CheifComplaintId = selectedComplaint.Id;
                        process.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(process.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");


            builder.CloseComponent();
        };
        private async Task<IEnumerable<CPT>> SearchCPTCodes(string value, CancellationToken cancellationToken)
        {
            return _cptCodes.Where(cpt =>
                (cpt.CPTCode?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (cpt.Description?.Contains(value, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }
        private async Task OnCPTSelected(CPT selected)
        {
            selectedCPT = selected;
            StateHasChanged();
        }

        private Task<IEnumerable<FDBMedicationName>> SearchMedications(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(value))
                return Task.FromResult(FDBmedications.AsEnumerable());

            var result = FDBmedications
                .Where(m => m.MED_NAME.Contains(value, StringComparison.OrdinalIgnoreCase));

            return Task.FromResult(result);
        }
        protected async Task<IEnumerable<string>> SearchVaccinesData(string value, CancellationToken cancellationToken)
        {
            var searchResults = _details
                .Where(t => !string.IsNullOrEmpty(t.VaccineName) && t.VaccineName.Contains(value, StringComparison.OrdinalIgnoreCase))
                .Select(t => t.VaccineName)
                .Distinct()
                .ToList();

            cancellationToken.ThrowIfCancellationRequested();

            return searchResults;
        }

       
        private async void AddNewProcedure()
        {
            if (selectedCPT == null)
            {
                Snackbar.Add(Localizer["Please select a CPT code first"], Severity.Warning);
                return;
            }

            var newProcedure = new OrderSetProcedures
            {
                Id = Guid.NewGuid(),
                CPTCode = selectedCPT.CPTCode,
                OrderedBy = User.givenName + " " + User.surname,
                Description = selectedCPT.Description,
                Notes = string.Empty,
                OrderDate = DateTime.Now
            };

            procedure.Add(newProcedure);
            //addedProcedure.Add(newProcedure);
            //ResetInputFields();
            selectedCPT = new CPT();
            await ProcedureGrid.Refresh();
        }

        private async Task OnICDNameChanged(string value)
        {
            VaccineName = value;

            var selectedVaccine = _details.FirstOrDefault(v => v.VaccineName == value);
            if (selectedVaccine != null)
            {
                SelectedCPTCode = selectedVaccine.CPTCode;
                SelectedCVXCode = selectedVaccine.CVXCode;
                SelectedCPTDescription = selectedVaccine.CPTDescription;
            }
            else
            {
                SelectedCPTCode = string.Empty;
                SelectedCVXCode = string.Empty;
                SelectedCPTDescription = string.Empty;
            }

            StateHasChanged();
        }
        private void UpdateComplaints()
        {
            chiefComplaints = SharedNotesService.GetChiefComplaints();
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            OnInitializedAsync();
            StateHasChanged();
        }
        /// <summary>
        /// Add medication to database & Update to grid,RichTextEditor
        /// </summary>
        private async void AddNewMedication()
        {
            if (selectedDatabase == @Localizer["RxNorm"])
            {
                if (string.IsNullOrEmpty(drugName) || string.IsNullOrEmpty(finalSBD))
                {
                    Snackbar.Add(@Localizer["Please fill in both Brand Name and Drug Details fields"], Severity.Warning);
                    return;
                }
                var newMedication = new OrderSetActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    BrandName = drugName,
                    DrugDetails = finalSBD,
                    Quantity = _Quantity ?? "",
                    Frequency = _Frequency ?? "",
                    StartDate = null,
                    EndDate = null,
                };

                medications.Add(newMedication);
                drugName = string.Empty;
                finalSBD = null;
                await MedicinesGrid.Refresh();
                
                //ResetInputFields();
            }
            else if (selectedDatabase == @Localizer["FDB"])
            {
                if (selectedMedication == null ||
                    selectedRoutedMedication == null ||
                    selectedRoutedDosageFormMedication == null ||
                    selectedFinalMedication == null)
                {
                    Snackbar.Add(@Localizer["Please fill all fields"], Severity.Warning);
                    return;
                }
                string selectedstrength;
                if (selectedFinalMedication.MED_STRENGTH != null && selectedFinalMedication.MED_STRENGTH_UOM != null)
                {
                    selectedstrength = selectedFinalMedication.MED_STRENGTH + " " + selectedFinalMedication.MED_STRENGTH_UOM;
                }
                else
                {
                    selectedstrength = selectedFinalMedication.MED_MEDID_DESC;
                }
                var newMedication = new OrderSetActiveMedication
                {
                    MedicineId = Guid.NewGuid(),
                    BrandName = selectedMedication.MED_NAME,
                    DrugDetails = selectedFinalMedication.MED_MEDID_DESC,
                    Route = GetRouteName(selectedRoutedMedication.MED_ROUTE_ID),
                    Take = GetTakeName(selectedRoutedDosageFormMedication.MED_DOSAGE_FORM_ID),
                    Strength = selectedstrength,
                    Quantity = _Quantity ?? "",
                    Frequency = _Frequency ?? "",
                    StartDate = null,
                    EndDate = null,
                };

                medications.Add(newMedication);

                drugName = string.Empty;
                finalSBD = null;
                BrandSBD.Clear();
                _Quantity = null;
                _Frequency = null;
                selectedMedication = null;
                selectedRoutedMedication = null;
                RoutedMedications.Clear();
                selectedRoutedDosageFormMedication = null;
                RoutedDosageFormMedications.Clear();
                selectedFinalMedication = null;
                FinalMedications.Clear();
                await MedicinesGrid.Refresh();
                //ResetInputFields();
            }
           
        }

        private async Task AddNewSurgery()
        {
            try
            {
                if (string.IsNullOrEmpty(VaccineName))
                {
                    Snackbar.Add(_localizer["Please Select Vaccine"], Severity.Warning);
                    return;
                }

                var emptyRow = immunization.FirstOrDefault(i => string.IsNullOrEmpty(i.Immunizations));

                if (emptyRow == null)
                {
                    emptyRow = new OrderSetImmunizationData();
                    immunization.Add(emptyRow);
                }

                emptyRow.ImmunizationId = Guid.NewGuid();
                emptyRow.GivenDate = DateTime.Now;
                emptyRow.Immunizations = VaccineName;
                emptyRow.CPTCode = SelectedCPTCode;
                emptyRow.CVXCode = SelectedCVXCode;
                emptyRow.CPTDescription = SelectedCPTDescription;
                emptyRow.Comments = Comments;


                if (ImmunizationGrid != null)
                {
                    await ImmunizationGrid.Refresh();
                }

                VaccineName = string.Empty;
                Comments = string.Empty;
                SelectedCPTCode = string.Empty;
                SelectedCVXCode = string.Empty;
                selectedCPT = new CPT();
                SelectedCPTDescription = string.Empty;

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new immunization");
                Snackbar.Add(_localizer["Failed to add immunization"], Severity.Error);
            }
        }
        /// <summary>
        /// Search function for auto complete bar 
        /// </summary>

        protected Task<IEnumerable<string>> SearchBrandNames(string value, CancellationToken cancellationToken)
        {
            IEnumerable<string> result;

            if (cancellationToken.IsCancellationRequested)
            {
                result = Enumerable.Empty<string>();
            }
            else if (string.IsNullOrWhiteSpace(value))
            {
                result = BrandNames.AsEnumerable();
            }
            else
            {
                result = BrandNames.Where(b => b.Contains(value, StringComparison.OrdinalIgnoreCase)).AsEnumerable();
            }

            return Task.FromResult(result);
        }

        /// <summary>
        /// Update value in Drug Name List
        /// </summary>

        private async Task OnDrugNameChanged(string value)
        {
            drugName = value;

            if (!string.IsNullOrEmpty(value))
            {
                var BrandSBDList = await RxNormService.GetRxNormSBDCMedications(value);
                BrandSBD = BrandSBDList.Select(m => m.STR).Distinct().ToList();
                finalSBD = null;
                StateHasChanged();

            }
            else
            {
                BrandSBD.Clear();
                finalSBD = null;
                StateHasChanged();
            }
        }

        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not ActiveMedication medication) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaintDescriptions);
            builder.AddAttribute(2, "Value", medication.CheifComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    medication.CheifComplaint = value;
                    var selectedComplaint = LocalData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        medication.CheifComplaintId = selectedComplaint.Id;
                        Console.WriteLine(medication.CheifComplaintId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };

        public async Task HandleDeleteOrderSet()
        {
            if (string.IsNullOrWhiteSpace(selectedOrderSet))
                return;

                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    return;
                }
            
            var orderSetId = CompleteOrderSetList
                                     .FirstOrDefault(x => x.orderSet.OrderSetName == selectedOrderSet).orderSet.Id;

            await orderSetService.DeleteOrderSetByIdAsync(orderSetId);
            CompleteOrderSetList = (await orderSetService.GetAllOrderSetAsync()).ToList();

            ResetInputFields();
            StateHasChanged();
        }
        public async Task HandleUpdateOrderSet()
        {
            if (string.IsNullOrWhiteSpace(selectedOrderSet))
                return;

            bool? result = await DialogService.ShowMessageBox(
                   @Localizer["Confirm Update"],
                   @Localizer["Do you want to Update this entry?"],
                   yesText: @Localizer["Yes"],
                   noText: @Localizer["No"]);

            if (result != true)
            {
                return;
            }
            var completeOrderSet = CompleteOrderSetList
                                     .FirstOrDefault(x => x.orderSet.OrderSetName == selectedOrderSet);

            var updatedOrderSet = new CompleteOrderSet
            {
                Id = completeOrderSet.orderSet.Id, 
                orderSet = new OrderSet
                {
                    Id = completeOrderSet.orderSet.Id,
                    OrderSetName = completeOrderSet.orderSet.OrderSetName,
                    Diagnosis = completeOrderSet.orderSet.Diagnosis,
                    OrganizationId = organizationId,
                    PCPId = Guid.Parse(User.id),
                    CreatedBy = Guid.Parse(User.id), 
                    UpdatedBy = Guid.Parse(User.id),
                    CreatedDate = completeOrderSet.orderSet.CreatedDate,
                    UpdatedDate = DateTime.UtcNow,
                    isActive = true,
                    Subscription = false
                },

                orderSetLabTests = planlabs,
                orderSetActiveMedication = medications,
                orderSetImmunizationData = immunization,
                orderSetProcedures = procedure,
                orderSetDiagnosticImaging = new OrderSetDiagnosticImaging
                {
                    RecordID = DiagnosticImagingList[0].RecordID,
                    DiCompany = DiagnosticImagingList[0].DiCompany,
                    ccResults = DiagnosticImagingList[0].ccResults,
                    Type = DiagnosticImagingList[0].Type,
                    Lookup = DiagnosticImagingList[0].Lookup,
                    OrderName = DiagnosticImagingList[0].OrderName,
                    StartsWith = DiagnosticImagingList[0].StartsWith,
                    OrderSetId = completeOrderSet.orderSet.Id
                }
            };

            await orderSetService.UpdateOrderSetAsync(updatedOrderSet);

            CompleteOrderSetList = (await orderSetService.GetAllOrderSetAsync()).ToList();
            StateHasChanged();
        }
        //When Route is selected
        private async Task OnRoutedMedicationSelected(FDBRoutedMedication med)
        {
            selectedRoutedMedication = med;
            selectedRoutedDosageFormMedication = null;
            RoutedDosageFormMedications.Clear();
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.ROUTED_MED_ID))
            {
                RoutedDosageFormMedications = await FDBService.GetFDBRoutedDosageFormMedications(med.ROUTED_MED_ID);
            }
        }

        //When Dosage Form is selected
        private async Task OnRoutedDosageFormMedicationSelected(FDBRoutedDosageFormMedication med)
        {
            selectedRoutedDosageFormMedication = med;
            selectedFinalMedication = null;
            FinalMedications.Clear();

            if (!string.IsNullOrEmpty(med?.ROUTED_DOSAGE_FORM_MED_ID))
            {
                FinalMedications = await FDBService.GetFDBFinalMedications(med.ROUTED_DOSAGE_FORM_MED_ID);
            }
        }

        private async Task OnFinalMedicationSelected(FDBMedication med)
        {
            selectedFinalMedication = med;
        }


        private string GetRouteName(string routeIdStr)
        {
            if (string.IsNullOrEmpty(routeIdStr))
                return string.Empty;

            if (routeNameLookup.TryGetValue(routeIdStr, out string routeName))
                return routeName;

            return $"Route {routeIdStr}";
        }

        private string GetTakeName(string takeIdStr)
        {
            if (string.IsNullOrEmpty(takeIdStr))
                return string.Empty;

            if (takeNameLookup.TryGetValue(takeIdStr, out string takeName))
                return takeName;

            return $"Route {takeIdStr}";
        }
        public async void HandleSubmit()
        {
            if (string.IsNullOrWhiteSpace(OrderSetName) ||
                string.IsNullOrWhiteSpace(DiagnosisLinked) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.DiCompany) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.Type) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.Lookup) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.OrderName) ||
                string.IsNullOrWhiteSpace(newDiagnosticImaging.StartsWith))
                {
                    Snackbar.Add("Please fill all required fields.", Severity.Error);
                    return;
                }
            var OrderSetID = Guid.NewGuid();

            foreach (var item in planlabs)
                item.OrderSetId = OrderSetID;

            foreach (var item in medications)
                item.OrderSetId = OrderSetID;

            foreach (var item in immunization)
                item.OrderSetId = OrderSetID;

            foreach (var item in procedure)
                item.OrderSetId = OrderSetID;

            var completeOrderSet = new CompleteOrderSet
            {
                Id = Guid.NewGuid(),

                orderSet = new OrderSet
                {
                    Id = OrderSetID,
                    OrderSetName = OrderSetName,
                    Diagnosis = DiagnosisLinked,
                    OrganizationId = organizationId,
                    PCPId = Guid.Parse(User.id),
                    CreatedBy = Guid.Parse(User.id),
                    UpdatedBy = Guid.Parse(User.id),
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = DateTime.UtcNow,
                    isActive = true,
                    Subscription = false
                },

                orderSetLabTests = planlabs,
                orderSetActiveMedication = medications,
                orderSetImmunizationData = immunization,
                orderSetProcedures = procedure,
                orderSetDiagnosticImaging =  new OrderSetDiagnosticImaging
                            {
                                RecordID = Guid.NewGuid(),
                                DiCompany = newDiagnosticImaging.DiCompany,
                                ccResults = newDiagnosticImaging.ccResults,
                                Type = newDiagnosticImaging.Type,
                                Lookup = newDiagnosticImaging.Lookup,
                                OrderName = newDiagnosticImaging.OrderName,
                                StartsWith = newDiagnosticImaging.StartsWith,
                                OrderSetId = OrderSetID
                            }
            };

            await orderSetService.AddOrderSetAsync(completeOrderSet);

            ResetInputFields();

            showRXForm = false;
            showDiagnosticImagingForm = false;
            showImmunizationForm = false;
            showLabsForm = false;
            showProcedureForm = false;

            CompleteOrderSetList = (await orderSetService.GetAllOrderSetAsync()).ToList();

            StateHasChanged(); 

        }
        private void ToggleRXForm()
        {
            showRXForm = !showRXForm;
        }
        private void ToggleDiagnosticImagingForm()
        {
            showDiagnosticImagingForm = !showDiagnosticImagingForm;
        }
        private void ToggleImmunizationForm()
        {
            showImmunizationForm = !showImmunizationForm;
        }
        private void ToggleShowLabsForm()
        {
            showLabsForm = !showLabsForm;
        }
        private void ToggleProcedureForm()
        {
            showProcedureForm = !showProcedureForm;
        }

        }

}