﻿using Android.App;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Android.Content;
namespace TeyaMobile.Platforms.Android
{
    /// <summary>
    /// Activity to handle MSAL authentication redirects
    /// </summary>
    [Activity(Exported = true)]
    [IntentFilter(new[] { Intent.ActionView },
        Categories = new[] { Intent.CategoryDefault, Intent.CategoryBrowsable },
        DataHost = "auth",
        DataScheme = "msale21369d6-92b3-446b-b981-0291bcb29b1b")]
    public class MsalActivity : BrowserTabActivity
    {
        // This activity will be called when the authentication flow returns to the app
        // The intent filter above matches the redirect URI: msal{client-id}://auth
    }
}
