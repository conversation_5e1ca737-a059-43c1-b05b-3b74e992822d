using System;

namespace TeyaMobileModel.Model
{
    /// <summary>
    /// Represents an audio recording with its metadata
    /// </summary>
    public class AudioRecording
    {
        /// <summary>
        /// Unique identifier for the recording
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// Timestamp when the recording was created
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        /// <summary>
        /// Local file path to the audio file
        /// </summary>
        public string FilePath { get; set; } = string.Empty;
        
        /// <summary>
        /// File name of the audio recording
        /// </summary>
        public string FileName { get; set; } = string.Empty;
        
        /// <summary>
        /// Duration of the recording in seconds
        /// </summary>
        public double DurationSeconds { get; set; }
        
        /// <summary>
        /// Transcription of the audio content
        /// </summary>
        public string Transcript { get; set; } = string.Empty;
        
        /// <summary>
        /// URL to the uploaded blob in Azure Storage
        /// </summary>
        public string BlobUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates if the recording has been uploaded to Azure Storage
        /// </summary>
        public bool IsUploaded => !string.IsNullOrEmpty(BlobUrl);
        
        /// <summary>
        /// Indicates if the recording has been transcribed
        /// </summary>
        public bool IsTranscribed => !string.IsNullOrEmpty(Transcript);
        
        /// <summary>
        /// Returns a formatted display name for the recording
        /// </summary>
        public string DisplayName => $"{Timestamp:yyyy-MM-dd HH:mm:ss}";
        
        /// <summary>
        /// Returns a short preview of the transcript (first 50 characters)
        /// </summary>
        public string TranscriptPreview
        {
            get
            {
                if (string.IsNullOrEmpty(Transcript))
                    return "No transcript available";
                
                return Transcript.Length <= 50 
                    ? Transcript 
                    : $"{Transcript.Substring(0, 47)}...";
            }
        }
    }
}
