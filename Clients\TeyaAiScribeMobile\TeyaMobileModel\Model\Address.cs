﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileModel.Model
{
    public class Address : IModel
    {

        public Guid? AddressId { get; set; }

        public string? AddressLine1 { get; set; }

        public string? AddressLine2 { get; set; }

        public string? City { get; set; }

        public string? State { get; set; }
        public Guid OrganizationID { get; set; }
        public bool Subscription { get; set; }
        public string? PostalCode { get; set; }

        public string? Country { get; set; }

    }
}
