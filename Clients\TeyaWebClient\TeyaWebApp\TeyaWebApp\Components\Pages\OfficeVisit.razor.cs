﻿using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models.TermStore;
using Syncfusion.Blazor.Navigations.Internal;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class OfficeVisit : ComponentBase
    {
        private List<OfficeVisitModel> OfficeVisits = new List<OfficeVisitModel>();

        [Inject] private IOfficeVisitService _visitService { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        [Inject] private IChartService _chartService { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [CascadingParameter] private Admin AdminLayout { get; set; }
        [Inject] private ActiveUser User { get; set; }
        private Guid userid;
        private Guid organizationId { get; set; }
        private bool Subscription { get;set; }

        /// <summary>
        /// Initializes the component by fetching the list of office visits for the user.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            userid = Guid.TryParse(User?.id, out Guid parsedId) ? parsedId : Guid.Empty;
            organizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(organizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == "Enterprise";
            var member = await MemberService.GetMemberByIdAsync(userid, organizationId, Subscription);
            OfficeVisits = await _visitService.GetPatientListByIdAsync(userid,organizationId, Subscription);
        }

        /// <summary>
        /// Redirects the user to the patient chart page.
        /// </summary>
        /// <param name="visitId">patient id - The ID of the office visit.</param>
        private async Task RedirectToChart(Guid visitId, String VisitStatus, String VisitType)
        {
            AdminLayout?.DrawerClose();
            await InvokeAsync(StateHasChanged);
            var patientData = await _chartService.GetPatientByIdAsync(visitId,organizationId,Subscription);
            PatientService.PatientData = patientData;
            PatientService.VisitStatus = VisitStatus;
            PatientService.VisitType = VisitType;  
            NavigationManager.NavigateTo("/Chart");
        }
    }
}
