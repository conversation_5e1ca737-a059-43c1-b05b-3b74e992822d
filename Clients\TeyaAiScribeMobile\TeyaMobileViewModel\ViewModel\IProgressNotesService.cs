﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using System.Net.Http;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IProgressNotesService
    {
        Task<List<Record>> GetRecordsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<Record>> GetRecordsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> SaveRecordAsync(Record updatedRecord, Guid? OrgID, bool Subscription);
    }
}
