﻿using System;
using System.Text.Json.Serialization;

namespace TeyaUIModels.Model
{
    public class Record : IModel
    {
        public Guid Id { get; set; }
        public bool Subscription { get; set; }
        public Guid OrganizationId { get; set; }

        public Guid PCPId { get; set; }
        public Guid PatientId { get; set; }
        public string PatientName { get; set; }
        public DateTime DateTime { get; set; }
        public string Notes { get; set; }

        public bool isEditable {  get; set; }

        //[JsonPropertyName("IsEditing")]
        //public bool IsEditing { get; set; }

        [JsonPropertyName("Transcription")]
        public string Transcription { get; set; }

        [JsonPropertyName("WordTimings")]
        public List<WordTiming> WordTimings { get; set; }

    }
}