﻿using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Components.Rendering;
using Microsoft.AspNetCore.Components.Web.HtmlRendering;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;

namespace TeyaMobileViewModel.ViewModel
{
    public class RazorComponentRenderer
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<RazorComponentRenderer> _logger;
        private readonly IStringLocalizer<TeyaUIViewModelResource.TeyaUIViewModelsResource> _localizer;

        public RazorComponentRenderer(IServiceProvider serviceProvider, ILogger<RazorComponentRenderer> logger, IStringLocalizer<TeyaUIViewModelResource.TeyaUIViewModelsResource> localizer)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _localizer = localizer;
        }

        public async Task<string> RenderComponentToHtmlAsync<TComponent>()
        {
            try
            {
                var renderer = _serviceProvider.GetRequiredService<HtmlRenderer>();

                return await renderer.Dispatcher.InvokeAsync(async () =>
                {
                    var htmlRootComponent = await renderer.RenderComponentAsync(typeof(TComponent));
                    return htmlRootComponent.ToHtmlString();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["RenderError"]);
                return string.Empty;
            }
        }
    }
}
