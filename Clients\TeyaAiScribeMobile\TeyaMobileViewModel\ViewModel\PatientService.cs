﻿using System;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public class PatientService
    {
        // Store the currently selected patient's data
        public Appointment SelectedPatient { get; set; }

        // Optionally, store additional metadata like VisitStatus and VisitType
        public string VisitStatus { get; set; }
        public string VisitType { get; set; }

        // Reset the selected patient when needed
        public void ClearSelectedPatient()
        {
            SelectedPatient = null;
            VisitStatus = null;
            VisitType = null;
        }
    }
}
