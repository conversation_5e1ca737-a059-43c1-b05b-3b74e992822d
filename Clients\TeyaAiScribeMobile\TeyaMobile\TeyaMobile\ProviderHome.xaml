<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="TeyaMobile.ProviderHome"
             Title="ProviderHome">
    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="20">
            <Label Text="Welcome, Sourav!" FontSize="24" FontAttributes="Bold" TextColor="#003087" HorizontalOptions="Start" />

            <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="*,*,*" RowSpacing="10" ColumnSpacing="10">
                <Frame Grid.Row="0" Grid.Column="0" BackgroundColor="White" CornerRadius="10" Padding="10" HasShadow="True">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToScheduleCommand}" />
                    </Frame.GestureRecognizers>
                    <VerticalStackLayout Spacing="5" HorizontalOptions="Center">
                        <Image Source="cal.png" WidthRequest="64" HeightRequest="64" />
                        <Label Text="View Appointments" FontSize="14" TextColor="#003087" HorizontalTextAlignment="Center" />
                    </VerticalStackLayout>
                </Frame>

                <Frame Grid.Row="0" Grid.Column="1" BackgroundColor="White" CornerRadius="10" Padding="10" HasShadow="True">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToBillingCommand}" />
                    </Frame.GestureRecognizers>
                    <VerticalStackLayout Spacing="5" HorizontalOptions="Center">
                        <Image Source="micp.png" WidthRequest="64" HeightRequest="64" />
                        <Label Text="Teya Ai Scribe" FontSize="14" TextColor="#003087" HorizontalTextAlignment="Center" />
                    </VerticalStackLayout>
                </Frame>

                <Frame Grid.Row="0" Grid.Column="2" BackgroundColor="White" CornerRadius="10" Padding="10" HasShadow="True">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToVideoVisitsCommand}" />
                    </Frame.GestureRecognizers>
                    <VerticalStackLayout Spacing="5" HorizontalOptions="Center">
                        <Image Source="visit.png" WidthRequest="64" HeightRequest="64" />
                        <Label Text="Visits Record" FontSize="14" TextColor="#003087" HorizontalTextAlignment="Center" />
                    </VerticalStackLayout>
                </Frame>

                <Frame Grid.Row="1" Grid.Column="0" BackgroundColor="White" CornerRadius="10" Padding="10" HasShadow="True">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToMedicationsCommand}" />
                    </Frame.GestureRecognizers>
                    <VerticalStackLayout Spacing="5" HorizontalOptions="Center">
                        <Image Source="med.png" WidthRequest="64" HeightRequest="64" />
                        <Label Text="Medications Record" FontSize="14" TextColor="#003087" HorizontalTextAlignment="Center" />
                    </VerticalStackLayout>
                </Frame>

                <Frame Grid.Row="1" Grid.Column="1" BackgroundColor="White" CornerRadius="10" Padding="10" HasShadow="True">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToTestResultsCommand}" />
                    </Frame.GestureRecognizers>
                    <VerticalStackLayout Spacing="5" HorizontalOptions="Center">
                        <Image Source="res.png" WidthRequest="64" HeightRequest="64" />
                        <Label Text="Patients Test Results" FontSize="14" TextColor="#003087" HorizontalTextAlignment="Center" />
                    </VerticalStackLayout>
                </Frame>

                <Frame Grid.Row="1" Grid.Column="2" BackgroundColor="White" CornerRadius="10" Padding="10" HasShadow="True">
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToMessagesCommand}" />
                    </Frame.GestureRecognizers>
                    <VerticalStackLayout Spacing="5" HorizontalOptions="Center">
                        <Image Source="msg.png" WidthRequest="64" HeightRequest="64" />
                        <Label Text="Messages" FontSize="14" TextColor="#003087" HorizontalTextAlignment="Center" />
                    </VerticalStackLayout>
                </Frame>
            </Grid>

            <Frame BackgroundColor="White" CornerRadius="10" Padding="15" HasShadow="True">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Upcoming Office Visit" FontSize="18" FontAttributes="Bold" TextColor="#003087" />
                    <HorizontalStackLayout Spacing="10">
                        <Frame BackgroundColor="#FFEBEE" CornerRadius="5" Padding="5">
                            <Label Text="JUN 30" FontSize="14" TextColor="#D32F2F" />
                        </Frame>
                        <VerticalStackLayout Spacing="5">
                            <Label Text="Starts at 4:15 PM CT" FontSize="14" TextColor="Black" />
                            <Label Text="Riverhills Medical Clinic with Dr. Drew Smith" FontSize="14" TextColor="Black" />
                        </VerticalStackLayout>
                    </HorizontalStackLayout>
                    <Button Text="eCheck-In" BackgroundColor="#4CAF50" TextColor="White" CornerRadius="5" WidthRequest="100" HorizontalOptions="Start" />
                    <Button Text="View Details" BackgroundColor="Transparent" TextColor="#003087" BorderColor="#003087" BorderWidth="1" CornerRadius="5" WidthRequest="100" HorizontalOptions="Start" />
                </VerticalStackLayout>
            </Frame>

            <Frame BackgroundColor="White" CornerRadius="10" Padding="15" HasShadow="True">
                <VerticalStackLayout Spacing="10">
                    <Label Text="New Lipid Panel results from Monday" FontSize="16" FontAttributes="Bold" TextColor="#003087" />
                    <HorizontalStackLayout Spacing="10">
                        <Image Source="doctor_icon.png" WidthRequest="40" HeightRequest="40" />
                        <VerticalStackLayout Spacing="5">
                            <Label Text="Phil Walker, MD" FontSize="14" TextColor="Black" />
                            <Label Text="Improvement! Keep watching the diet, exercising daily, and taking your medications." FontSize="14" TextColor="Black" LineBreakMode="WordWrap" />
                        </VerticalStackLayout>
                    </HorizontalStackLayout>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>