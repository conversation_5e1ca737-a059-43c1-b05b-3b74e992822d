﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Net.Http.Json;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using TeyaWebApp.ViewModel;
using TeyaMobileViewModel.TeyaUIViewModelResources;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public class ProductService : IProductService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _MemberService;

        public ProductService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        public async Task<List<Product>> GetProductsAsync()
        {
            var accessToken = _tokenService.AccessToken;
            var requestUrl = $"{_MemberService}/api/Products";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Product>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["ProductRetrievalFailure"]);
            }
        }

        public async Task UpdateMembersAccessAsync(Guid productId, List<MemberAccessUpdate> memberAccessUpdates)
        {
            try
            {
                var productIdParameter = "productId";
                var requestUrl = $"{_MemberService}/api/Products/updateAccess?{productIdParameter}={productId}";
                var accessToken = _tokenService.AccessToken;
                var bodyContent = JsonSerializer.Serialize(memberAccessUpdates);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, requestUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new HttpRequestException(_localizer["UpdateAccessFailure"], ex);
            }
        }
    }
}