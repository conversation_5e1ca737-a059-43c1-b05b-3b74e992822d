﻿@page "/OfficeVisit"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "VisitAccessPolicy")]
@using Microsoft.AspNetCore.Components
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Grids
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@using TeyaUIModels.Model
@inject TeyaUIViewModels.ViewModel.IOfficeVisitService VisitService
@inject IMemberService MemberService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<PageTitle>@Localizer["Office Visits"]</PageTitle>

<div>
    <SfGrid DataSource="@OfficeVisits" AllowPaging="true" GridLines="GridLine.Both" AllowTextWrap="true" Height="345px">
        <GridPageSettings PageSize="10" PageSizes="true"></GridPageSettings>
        <GridColumns>
            <GridColumn Field=@nameof(OfficeVisitModel.VisitType) HeaderText="@Localizer["Visit Type"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.AppointmentTime) HeaderText="@Localizer["Appointment Time"]" Format="H:mm" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn HeaderText="@Localizer["Patient Name"]" AutoFit="true" TextAlign="TextAlign.Center">
                <Template>
                    @if (context is OfficeVisitModel visit && visit.PatientName != null)
                    {
                        <span style="text-decoration: none; color: blue; cursor: pointer;"
                              @onclick="() => RedirectToChart(visit.Id, visit.VisitStatus, visit.VisitType)">
                            @visit.PatientName
                        </span>
                    }
                    else
                    {
                        <span>N/A</span>
                    }
                </Template>
            </GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.PR) HeaderText="@Localizer["P/R"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Reason) HeaderText="@Localizer["Reason"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Notes) HeaderText="@Localizer["Notes"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Sex) HeaderText="@Localizer["Sex"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Dob) HeaderText="@Localizer["Date of Birth"]" Format="MMM dd, yyyy" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.VisitStatus) HeaderText="@Localizer["Visit Status"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.ArrivalTime) HeaderText="@Localizer["Arrival Time"]" Format="H:mm" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.Duration) HeaderText="@Localizer["Duration"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
            <GridColumn Field=@nameof(OfficeVisitModel.RoomNumber) HeaderText="@Localizer["Room Number"]" AutoFit="true" TextAlign="TextAlign.Center"></GridColumn>
        </GridColumns>
    </SfGrid>
</div>

<div style="display: flex; justify-content: space-between; align-items: center;">
    <div style="display: flex; gap: 0.5rem;">
        <MudButton Variant="Variant.Filled" Color="Color.Primary">@Localizer["View Log"]</MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary">@Localizer["View"]</MudButton>
        <MudButton Variant="Variant.Outlined" Color="Color.Primary">@Localizer["Web View"]</MudButton>
        <MudButton Variant="Variant.Outlined" Color="Color.Primary">@Localizer["Print"]</MudButton>
    </div>
    <div style="display: flex; gap: 0.5rem;">
        <MudButton Variant="Variant.Filled" Color="Color.Primary">@Localizer["OK"]</MudButton>
        <MudButton Variant="Variant.Outlined" Color="Color.Primary">@Localizer["Cancel"]</MudButton>
    </div>
</div>
