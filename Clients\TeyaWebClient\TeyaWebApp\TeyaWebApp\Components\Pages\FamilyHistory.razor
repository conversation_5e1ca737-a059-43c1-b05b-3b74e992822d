﻿@page "/FamilyHistory"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject TeyaUIViewModels.ViewModel.IFamilyMemberService FamilyMemberService
@inject TeyaUIViewModels.ViewModel.IRelationService RelationService
@using Syncfusion.Blazor.RichTextEditor


<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" aria-label="delete" @onclick="OpenAddTaskDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>


<MudDialog @ref="_addMemberDialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">

     <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["FamilyHistory"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <SfGrid @ref="familyGrid" TValue="FamilyMember" DataSource="@FamilyMemberList"
						Toolbar="@ToolbarItems" AllowPaging="true" GridLines="GridLine.Both"
                        Style="font-size: 0.85rem; margin-top: 24px;">

                    <GridEditSettings AllowEditing="true" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>

                    <GridEvents OnActionBegin="ActionBeginHandler" OnActionComplete="ActionCompletedHandler" TValue="FamilyMember"></GridEvents>

                    <GridColumns>
                        <GridColumn Field="RecordID" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="@nameof(FamilyMember.Relation)" HeaderText="@Localizer["Relation"]" Width="150" TextAlign="TextAlign.Center" ValidationRules="@(new Syncfusion.Blazor.Grids.ValidationRules { Required = true })" EditTemplate="@RelationEditTemplate"></GridColumn>
                        <GridColumn Field="Status" HeaderText="@Localizer["Status"]" TextAlign="TextAlign.Center" ValidationRules="@(new Syncfusion.Blazor.Grids.ValidationRules { Required = true })"></GridColumn>
                        <GridColumn Field="@nameof(FamilyMember.DOB)" HeaderText="@Localizer["DateOfBirth"]" Format="MM/dd/yyyy" TextAlign="TextAlign.Center">
                            <EditTemplate>
                                <SfDatePicker TValue="DateTime?"
                                              Value="@((context as FamilyMember).DOB)"
                                              Max="@DateTime.Today"
                                              ValueChanged="@(d => { if (d.HasValue) UpdateAge(context as FamilyMember, d.Value); })">
                                </SfDatePicker>
                            </EditTemplate>
                        </GridColumn>

                        <GridColumn Field="Age" HeaderText="@Localizer["Age"]" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" ValidationRules="@(new Syncfusion.Blazor.Grids.ValidationRules { Required = true })"></GridColumn>

                        <GridColumn HeaderText="Actions" Width="110" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions()
                                         { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>
