﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IMedicalHistoryService
    {
        Task<List<MedicalHistory>> GetAllByIdAsync(Guid id);
        Task<List<MedicalHistory>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddMedicalHistoryAsync(List<MedicalHistory> medicalHistories);
        Task UpdateMedicalHistoryAsync(MedicalHistory medicalHistory);
        Task UpdateMedicalHistoryListAsync(List<MedicalHistory> medicalHistories);
        Task DeleteMedicalHistoryByEntityAsync(MedicalHistory medicalHistory);
    }
}