using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaWebApp.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Components.Pages.CDSS
{
    public partial class PatientSpecificAlert
    {
        [Inject] ActiveUser activeUser { get; set; }
        /// <summary>
        /// Injected service for managing patient data.
        /// </summary>
        [Inject]
        private PatientService PatientService { get; set; }
        /// <summary>
        /// Gets the patient data from the service or initializes a new instance if null.
        /// </summary>
        public Patient PatientData => PatientService.PatientData ?? new Patient();
        [Inject]
        private NavigationManager Navigation { get; set; }

        [Inject]
        private IPatientSpecificAlertsService AlertsService { get; set; }
        [Inject]
        private IMemberService MemberService { get; set; }
        [Inject] private ILogger<PatientSpecificAlert> Logger { get; set; }

        private MudForm _form;
        private PatientSpecificAlertsData _alert = new()
        {
            Id = Guid.NewGuid(),
            IsActive = true,
            DueDate = DateTime.Today,
            RecallAfterDays = 30,
            CreatedDate = DateTime.Now
        };

        private string searchTerm = "";
        private string _providerAlertName = "";
        private bool _showProvidersList = false;

        private List<string> _providerList { get; set; }

        protected override void OnInitialized()
        {
            try
            {
                var orgId = PatientData.OrganizationID ?? Guid.Empty;
                var providerPatientsTask = MemberService.GetProviderPatientByOrganizationIdAsync(orgId, false);
                providerPatientsTask.ContinueWith(task =>
                {
                    if (task.Status == TaskStatus.RanToCompletion && task.Result != null)
                    {
                        _providerList = task.Result.Select(p => p.PCPId.HasValue ? p.PCPId.ToString() : string.Empty).ToList();
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error initializing provider list: {ex.Message}");
            }
        }
        private void ToggleProvidersList()
        {
            _showProvidersList = !_showProvidersList;
        }

        private async Task SubmitForm()
        {
            await _form.Validate();
            if (_form.IsValid)
            {
                _alert.UpdatedBy = Guid.Empty;
                _alert.ProviderName = _providerAlertName;
                _alert.PatientId = PatientData.Id;
                _alert.CreatedBy = Guid.Parse(activeUser.id);

                try
                {
                    await AlertsService.AddPatientSpecificAlertAsync(_alert);

                    Logger.LogError($"Alert Saved: {_alert.AlertName}, Type: {_alert.AlertType}, Due: {_alert.DueDate}");
                    Navigation.NavigateTo("/cdss");
                }
                catch (Exception ex)
                {
                    Logger.LogError($"Error saving alert: {ex.Message}");
                }
            }
        }

        private void GoToCdss()
        {
            Navigation.NavigateTo("/cdss");
        }

        private void SelectProvider(string provider)
        {
            _providerAlertName = provider;
            _showProvidersList = false;
        }

        private List<string> FilteredProviders => string.IsNullOrEmpty(searchTerm)
            ? _providerList
            : _providerList.Where(p => p.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
    }
}