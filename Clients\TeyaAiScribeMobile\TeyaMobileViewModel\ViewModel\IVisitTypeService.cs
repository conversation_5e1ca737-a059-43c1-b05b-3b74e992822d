﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModels
{
    public interface IVisitTypeService
    {
        Task<List<VisitType>> GetAllVisitTypesAsync();
        Task<List<string>> GetVisitTypeNamesAsync();
        Task<List<VisitType>> GetVisitTypesByOrganizationIdAsync(Guid orgId);
        Task<bool> UpdateCptCodeAsync(Guid orgId, string visitName, string newCptCode);
        Task<bool> AddVisitTypeAsync(VisitType visitType);
        Task<bool> DeleteVisitTypeAsync(Guid orgId, string visitName);
    }
}
