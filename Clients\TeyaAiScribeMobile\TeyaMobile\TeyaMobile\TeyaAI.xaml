<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="TeyaMobile.TeyaAI"
             Title="Teya AI Scribe">
    <ContentPage.Resources>
        <Style TargetType="Button" Class="rounded-button">
            <Setter Property="CornerRadius" Value="30"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="TextColor" Value="White"/>
            <Setter Property="FontSize" Value="20"/>
        </Style>
    </ContentPage.Resources>
    <Grid RowDefinitions="Auto, Auto, Auto, Auto, Auto,*,Auto"
       ColumnDefinitions="*, *"
       Padding="20">
        <Frame Grid.Row="0"
               Grid.ColumnSpan="2"
               BackgroundColor="IndianRed"
               CornerRadius="20"
               Padding="5"
               Margin="70,80,70,0"
               HeightRequest="30">
            <Label x:Name="StatusLabel"
                   Text="Teya AI Scribe"
                   TextColor="White"
                   FontAttributes="Bold"
                   FontSize="14"
                   HorizontalTextAlignment="Center"/>
        </Frame>

        <Image Grid.Row="1"
           Grid.ColumnSpan="2"
           x:Name="GifAnimation"
           Source="aiwave.gif"
           IsAnimationPlaying="False"
           HorizontalOptions="Center"
           VerticalOptions="Center"
           HeightRequest="200"
           WidthRequest="300"
           Margin="0,0,0,5"/>

        <Label Grid.Row="2"
           Grid.ColumnSpan="2"
           x:Name="TimerLabel"
           Text="00:00"
           FontSize="40"
           FontAttributes="Bold"
           TextColor="Black"
           HorizontalTextAlignment="Center"
           Margin="0,0,0,20"/>

        <Label Grid.Row="3"
           Grid.ColumnSpan="2"
           x:Name="TranscriptionLabel"
           Text=""
           FontSize="16"
           TextColor="Black"
           HorizontalTextAlignment="Center"
           Margin="20,20,20,20"
           IsVisible="False"/>

        <StackLayout Grid.Row="4"
                 Grid.ColumnSpan="2"
                 Orientation="Horizontal"
                 HorizontalOptions="Center"
                 Spacing="20">
            <Button x:Name="StartButton"
                Text="Start"
                Style="{StaticResource rounded-button}"
                BackgroundColor="Teal"
                Clicked="OnStartClicked"
                WidthRequest="200"
                HeightRequest="60"
                IsVisible="True"/>

            <Button x:Name="PauseResumeButton"
                Text="Pause"
                Style="{StaticResource rounded-button}"
                BackgroundColor="Teal"
                Clicked="OnPauseResumeClicked"
                WidthRequest="160"
                HeightRequest="60"
                IsVisible="False"/>

            <Button x:Name="StopButton"
                Text="Stop"
                Style="{StaticResource rounded-button}"
                BackgroundColor="Teal"
                Clicked="OnStopClicked"
                WidthRequest="160"
                HeightRequest="60"
                IsVisible="False"/>
        </StackLayout>

        <Editor 
                x:Name="TranscriptionEditor"
                Grid.Row="5" 
                Grid.ColumnSpan="2"
                Text="{Binding TranscriptionText, Mode=OneWay}"
                IsReadOnly="True"
                AutoSize="TextChanges"
                HeightRequest="50"
                FontSize="16"
                BackgroundColor="FloralWhite"
                Placeholder="Transcription will appear here..." />

        <!--<Button x:Name="TranscribeButton"
                Text="Transcribe"
                Style="{StaticResource rounded-button}"
                BackgroundColor="Teal"
                Clicked="OnTranscribeClicked"
                WidthRequest="160"
                HeightRequest="60"
                IsVisible="True"/>-->                


        <!--<ImageButton x:Name="QuestionMarkIcon"
                 Source="lightbulb.png"
                 Clicked="OnQuestionMarkClicked"
                 Grid.Row="5"
                 Grid.Column="1"
                 HorizontalOptions="End"
                 WidthRequest="30"
                 HeightRequest="30"
                 Margin="0,0,20,20"/>-->
    </Grid>
</ContentPage>