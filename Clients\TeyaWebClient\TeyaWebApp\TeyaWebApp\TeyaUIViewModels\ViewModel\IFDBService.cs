﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IFDBService 
    {
        Task<List<FDBMedicationName>> GetAllFDBMedications();
        Task<List<FDBRoutedMedication>> GetFDBRoutedMedications(string str);
        Task<List<FDBRoutedDosageFormMedication>> GetFDBRoutedDosageFormMedications(string str);
        Task<List<FDBMedication>> GetFDBFinalMedications(string str);
        Task<List<FDBRouteLookUp>> GetFDBRouteLookUp();
        Task<List<FDBTakeLookUp>> GetFDBTakeLookUp();
        Task<List<FDBAllergies>> GetAllergies();
        Task<List<FDB_ICD>> GetICD();
        Task<List<FDBVaccines>> GetVaccines();
        Task<FDBVaccine_CPT_CVX> GetCPTForVaccine(string str);
    }
}
