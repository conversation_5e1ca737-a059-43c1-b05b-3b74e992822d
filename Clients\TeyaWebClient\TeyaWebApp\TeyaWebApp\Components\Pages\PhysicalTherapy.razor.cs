﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class PhysicalTherapy
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public IPhysicalTherapyService _PhysicalTherapyService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        public string ICDName { get; set; }
        private string editorContent;
        private SfRichTextEditor RichTextEditor;
        private MudDialog __PhysicalTherapy;
        private Guid PatientId { get; set; }
        private DateTime? _CreatedDate;
        private Guid? OrgID { get; set; }
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        public SfGrid<TeyaUIModels.Model.PhysicalTherapyData> PhysicalTherapyGrid { get; set; }

        private List<TeyaUIModels.Model.PhysicalTherapyData> AddList = new();
        private List<TeyaUIModels.Model.PhysicalTherapyData> _PhysicalTherapy { get; set; }
        private List<TeyaUIModels.Model.PhysicalTherapyData> deletePhysicalTherapylist { get; set; } = new List<TeyaUIModels.Model.PhysicalTherapyData>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
         };

        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = _PatientService.PatientData.Id;
            OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            try
            {
                _icdCodes = await _ICDService.GetAllICDCodesAsync();
                _PhysicalTherapy = await _PhysicalTherapyService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
                UpdateEditorContent();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving ICD codes: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void UpdateEditorContent()
        {
            editorContent = string.Join("<br>", _PhysicalTherapy
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $@"<p><strong>{Localizer["Created Date"]}:</strong> {(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToShortDateString() : Localizer["No date"])} <br><strong>{Localizer["Therapy Assessment"]}:</strong> {s.TherapyAssessment} <br><strong>{Localizer["Physical Therapy Diagnosis"]}:</strong> {s.PhysicalTherapyDiagnosis} <br><strong>{Localizer["Disabilities"]}:</strong> {s.Disabilities}</p>"));
        }


        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>

        private async Task OpenNewDialogBox()
        {
            await __PhysicalTherapy.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await __PhysicalTherapy.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected async Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            var searchResults = _icdCodes
                .Where(icd => (icd.Code != null && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                              (icd.Description != null && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase)))
                .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                .ToList();
            cancellationToken.ThrowIfCancellationRequested();
            return searchResults;
        }

        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewDiagnosis()
        {
            var newDiagnosis = new TeyaUIModels.Model.PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = _PatientService.PatientData.OrganizationID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                PhysicalTherapyDiagnosis = ICDName,
                IsActive = true,
            };

            AddList.Add(newDiagnosis);
            _PhysicalTherapy.Add(newDiagnosis);
            await PhysicalTherapyGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private async void ResetInputFields()
        {
            ICDName = string.Empty;
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<TeyaUIModels.Model.PhysicalTherapyData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var delelePhysicalTherapy = args.Data;
                var existingItem = AddList.FirstOrDefault(c => c.PhysicalTherapyID == delelePhysicalTherapy.PhysicalTherapyID);
                args.Data.IsActive = false;

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    delelePhysicalTherapy.IsActive = false;
                    delelePhysicalTherapy.UpdatedDate = DateTime.Now;
                    deletePhysicalTherapylist.Add(delelePhysicalTherapy);
                }

            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<TeyaUIModels.Model.PhysicalTherapyData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _PhysicalTherapyService.AddPhysicalTherapyAsync(AddList, OrgID, Subscription);
            }
            await _PhysicalTherapyService.UpdatePhysicalTherapyListAsync(_PhysicalTherapy, OrgID, Subscription);
            await _PhysicalTherapyService.UpdatePhysicalTherapyListAsync(deletePhysicalTherapylist, OrgID, Subscription);
            deletePhysicalTherapylist.Clear();
            AddList.Clear();
            UpdateEditorContent();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deletePhysicalTherapylist.Clear();
            AddList.Clear();
            _PhysicalTherapy = await _PhysicalTherapyService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }
    }
}