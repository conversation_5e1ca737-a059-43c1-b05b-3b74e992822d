﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IRxNormService
    {
        Task<List<string>> GetAllBrandNames();
        Task<List<string>> GetSBDNamesAsync(string name);
        Task<List<string>> GetAllDrugNames();
        string GetRxcuiByName(string name);
    }
}