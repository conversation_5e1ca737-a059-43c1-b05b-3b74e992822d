﻿﻿using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;

namespace TeyaWebApp.Components.Pages
{
    public partial class CustomLabAlert
    {
        [Inject] public ICustomLabAlertService CustomLabAlertService { get; set; }
        [Inject] private ILogger<CustomLabAlert> _logger { get; set; }
        [Inject] private IStringLocalizer<CustomLabAlert> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }

        [CascadingParameter] private MudDialogInstance? MudDialog { get; set; }

        private SfGrid<CustomLabAlerts>? CustomLabAlertGrid;

        private List<CustomLabAlerts> customLabAlerts = new();
        private List<CustomLabAlerts> deleteAlertList = new();
        private List<CustomLabAlerts> addList = new();

        // Form fields
        private string alertName = string.Empty;
        private string alertDescription = string.Empty;
        private string webReference = string.Empty;
        private int? ageLowerBound;
        private int? ageUpperBound;
        private string orderSet = string.Empty;
        private string gender = "Both";

        [Inject]
        private PatientService PatientService { get; set; } = default!;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }

        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientService.PatientData.Id;
            OrgID = PatientService.PatientData.OrganizationID;
            try
            {
                await LoadAlertsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lab alert data");
            }
        }

        private async Task LoadAlertsAsync()
        {
            var existingAlerts = await CustomLabAlertService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, false);
            customLabAlerts = existingAlerts?.ToList() ?? new List<CustomLabAlerts>();

            // Add empty rows if needed
            int emptyRowsNeeded = 5 - customLabAlerts.Count;
            if (emptyRowsNeeded > 0)
            {
                customLabAlerts.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                    .Select(_ => new CustomLabAlerts
                    {
                        Name = string.Empty,
                        Description = string.Empty,
                        WebReference = string.Empty,
                        OrderSet = string.Empty,
                        Gender = string.Empty
                    }));
            }
        }





        public void ActionCompletedHandler(ActionEventArgs<CustomLabAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedAlert = args.Data;
                var existingItem = addList.FirstOrDefault(v => v.Id == deletedAlert.Id);

                if (existingItem != null)
                {
                    addList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;  // Set IsActive to false instead of actually deleting
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteAlertList.Add(args.Data);
                }
            }
        }

        public void ActionBeginHandler(ActionEventArgs<CustomLabAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        private async Task SaveData()
        {
            try
            {
                if (addList.Count > 0)
                {
                    // Set IsActive to true for all new alerts
                    foreach (var alert in addList)
                    {
                        alert.IsActive = true;
                    }
                    await CustomLabAlertService.AddCustomLabAlertsAsync(addList, OrgID, false);
                }

                if (deleteAlertList.Count > 0)
                {
                    await CustomLabAlertService.UpdateCustomLabAlertsListAsync(deleteAlertList, OrgID, false);
                }

                // Update existing alerts
                var existingAlerts = customLabAlerts.Where(a => !string.IsNullOrEmpty(a.Name) && a.Id != Guid.Empty).ToList();
                if (existingAlerts.Count > 0)
                {
                    await CustomLabAlertService.UpdateCustomLabAlertsListAsync(existingAlerts, OrgID, false);
                }

                deleteAlertList.Clear();
                addList.Clear();

                await LoadAlertsAsync();
                ResetInputFields();

                Snackbar.Add(_localizer["Lab alerts saved successfully"], Severity.Success);

                // Close the dialog
                MudDialog?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving lab alert data");
                Snackbar.Add(_localizer["Failed to save lab alert records"], Severity.Error);
            }
        }

        private async Task CancelData()
        {
            deleteAlertList.Clear();
            addList.Clear();
            await LoadAlertsAsync();
            ResetInputFields();

            // Close the dialog
            MudDialog?.Close();
        }

        private void ResetInputFields()
        {
            alertName = string.Empty;
            alertDescription = string.Empty;
            webReference = string.Empty;
            ageLowerBound = null;
            ageUpperBound = null;
            orderSet = string.Empty;
            gender = "Both";
        }

        private async Task AddNewAlert()
        {
            try
            {
                if (string.IsNullOrEmpty(alertName))
                {
                    Snackbar.Add(_localizer["Please enter alert name"], Severity.Warning);
                    return;
                }

                var emptyRow = customLabAlerts.FirstOrDefault(i => string.IsNullOrEmpty(i.Name));

                if (emptyRow == null)
                {
                    emptyRow = new CustomLabAlerts();
                    customLabAlerts.Add(emptyRow);
                }

                emptyRow.Id = Guid.NewGuid();
                emptyRow.PatientId = PatientId;
                emptyRow.pcpId = Guid.Parse(User.id);
                emptyRow.OrganizationId = OrgID ?? Guid.Empty;
                emptyRow.CreatedDate = DateTime.Now;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.Name = alertName;
                emptyRow.Description = alertDescription;
                emptyRow.WebReference = webReference;
                emptyRow.AgeLowerBound = ageLowerBound;
                emptyRow.AgeUpperBound = ageUpperBound;
                emptyRow.OrderSet = orderSet;
                emptyRow.Gender = gender;
                emptyRow.IsActive = true;  // Set IsActive to true for new alerts

                addList.Add(emptyRow);

                if (CustomLabAlertGrid != null)
                {
                    await CustomLabAlertGrid.Refresh();
                }

                ResetInputFields();

                Snackbar.Add(_localizer["Alert added successfully"], Severity.Success);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new lab alert");
                Snackbar.Add(_localizer["Failed to add lab alert"], Severity.Error);
            }
        }
    }
}
