﻿using System;
using System.ComponentModel.DataAnnotations;

namespace TeyaMobileModel.Model
{
    public class Role : IModel
    {
        public Guid RoleId { get; set; }

        [Required(ErrorMessage = "Role Name is required")]
        [StringLength(100, ErrorMessage = "Role Name cannot exceed 100 characters")]
        public string RoleName { get; set; }

        [Required(ErrorMessage = "Created Date is required")]
        public DateTime? CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdatedBy { get; set; }
        public bool IsActive { get; set; } = true;

        [Required(ErrorMessage = "Organization ID is required")]
        public Guid OrganizationID { get; set; }
        public bool Subscription { get; set; }
    }
}
