﻿@using Microsoft.AspNetCore.Components.Authorization

<div class="login-container">
    @if (IsAuthenticated)
    {
        <div class="user-profile-card">
            <div class="profile-header">
                <div class="user-avatar">
                    @if (!string.IsNullOrEmpty(UserName))
                    {
                        <span class="avatar-text">@UserName.Substring(0, 1).ToUpper()</span>
                    }
                    else
                    {
                        <span class="avatar-text">U</span>
                    }
                    <div class="online-indicator"></div>
                </div>
                <div class="user-details">
                    <h3 class="user-name">@UserName</h3>
                    <p class="user-email">@UserEmail</p>
                    <span class="status-badge">Active</span>
                </div>
            </div>
            <div class="profile-actions">
                <button class="btn btn-outline" @onclick="HandleLogout" disabled="@IsLoading">
                    @if (IsLoading)
                    {
                        <div class="loading-spinner"></div>
                        <span>Signing out...</span>
                    }
                    else
                    {
                        <i class="icon-logout"></i>
                        <span>Sign Out</span>
                    }
                </button>
            </div>
        </div>
    }
    else
    {
        <div class="login-card">
            <div class="login-header">
                <div class="brand-section">
                    <div class="teya-logo">
                        <div class="logo-icon">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
                                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
                                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
                            </svg>
                        </div>
                        <span class="logo-text">Teya Health</span>
                    </div>
                    <div class="welcome-text">
                        <h2>Welcome back</h2>
                        <p>Sign in to access your medical dashboard</p>
                    </div>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(ErrorMessage))
            {
                <div class="error-alert">
                    <div class="error-icon">⚠️</div>
                    <div class="error-content">
                        <span class="error-title">Authentication Error</span>
                        <span class="error-message">@ErrorMessage</span>
                    </div>
                    <button class="error-close" @onclick="ClearError">×</button>
                </div>
            }

            <div class="login-form">
                <button class="btn btn-primary" @onclick="HandleLogin" disabled="@IsLoading">
                    @if (IsLoading)
                    {
                        <div class="btn-loading">
                            <div class="loading-spinner"></div>
                            <span>Authenticating...</span>
                        </div>
                    }
                    else
                    {
                        <div class="btn-content">
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span>Sign in with Teya Health</span>
                        </div>
                    }
                </button>

                <div class="login-divider">
                    <span>Secure authentication powered by Microsoft</span>
                </div>

                <div class="security-features">
                    <div class="feature-item">
                        <div class="feature-icon">🔒</div>
                        <span>Enterprise-grade security</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <span>Single sign-on enabled</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🛡️</div>
                        <span>HIPAA compliant</span>
                    </div>
                </div>
            </div>

            <div class="login-footer">
                <p>Need help? <a href="/support" class="support-link">Contact Support</a></p>
                <p class="privacy-text">
                    By signing in, you agree to our
                    <a href="/terms" class="link">Terms of Service</a> and
                    <a href="/privacy" class="link">Privacy Policy</a>
                </p>
            </div>
        </div>
    }
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        position: relative;
        overflow: hidden;
    }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
        max-width: 440px;
        width: 100%;
        position: relative;
        z-index: 1;
        animation: slideUp 0.6s ease-out;
    }

    @@keyframes slideUp {
        from

    {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }

    }

    .login-header {
        text-align: center;
        margin-bottom: 32px;
    }

    .brand-section {
        margin-bottom: 24px;
    }

    .teya-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 20px;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

        .logo-icon svg {
            width: 24px;
            height: 24px;
        }

    .logo-text {
        font-size: 24px;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcome-text h2 {
        color: #1a1a1a;
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        letter-spacing: -0.5px;
    }

    .welcome-text p {
        color: #6b7280;
        margin: 0;
        font-size: 16px;
        line-height: 1.5;
    }

    .error-alert {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #fecaca;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 24px;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        animation: shake 0.5s ease-in-out;
    }

    @@keyframes shake {
        0%, 100%

    {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }

    }

    .error-icon {
        font-size: 20px;
        flex-shrink: 0;
    }

    .error-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .error-title {
        font-weight: 600;
        color: #dc2626;
        font-size: 14px;
    }

    .error-message {
        color: #7f1d1d;
        font-size: 13px;
        line-height: 1.4;
    }

    .error-close {
        background: none;
        border: none;
        color: #dc2626;
        cursor: pointer;
        font-size: 20px;
        line-height: 1;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

        .error-close:hover {
            background: rgba(220, 38, 38, 0.1);
        }

    .btn {
        width: 100%;
        padding: 16px 24px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        position: relative;
        overflow: hidden;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.5);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

    .btn-content {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .btn-icon {
        width: 20px;
        height: 20px;
    }

    .btn-loading {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0%

    {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }

    }

    .login-divider {
        text-align: center;
        margin: 24px 0;
        position: relative;
    }

        .login-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
        }

        .login-divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 16px;
            color: #6b7280;
            font-size: 12px;
            position: relative;
        }

    .security-features {
        display: grid;
        grid-template-columns: 1fr;
        gap: 12px;
        margin: 24px 0;
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 8px;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .feature-icon {
        font-size: 16px;
        flex-shrink: 0;
    }

    .feature-item span {
        color: #4b5563;
        font-size: 14px;
        font-weight: 500;
    }

    .login-footer {
        text-align: center;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid #f3f4f6;
    }

        .login-footer p {
            margin: 8px 0;
            color: #6b7280;
            font-size: 13px;
            line-height: 1.5;
        }

    .support-link, .link {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.2s;
    }

        .support-link:hover, .link:hover {
            color: #5a67d8;
            text-decoration: underline;
        }

    .privacy-text {
        font-size: 12px !important;
        color: #9ca3af !important;
    }

    /* User Profile Card Styles */
    .user-profile-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        padding: 32px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
        max-width: 400px;
        width: 100%;
        position: relative;
        z-index: 1;
        animation: slideUp 0.6s ease-out;
    }

    .profile-header {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 24px;
    }

    .user-avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 24px;
        position: relative;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .online-indicator {
        position: absolute;
        bottom: 4px;
        right: 4px;
        width: 16px;
        height: 16px;
        background: #10b981;
        border: 3px solid white;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @@keyframes pulse {
        0%, 100%

    {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    }

    .user-details {
        flex: 1;
    }

    .user-name {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 700;
        color: #1a1a1a;
    }

    .user-email {
        margin: 0 0 8px 0;
        color: #6b7280;
        font-size: 14px;
    }

    .status-badge {
        background: #d1fae5;
        color: #065f46;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
    }

    .profile-actions {
        text-align: center;
    }

    .btn-outline {
        background: transparent;
        border: 2px solid #e5e7eb;
        color: #6b7280;
        padding: 12px 24px;
    }

        .btn-outline:hover:not(:disabled) {
            border-color: #dc2626;
            color: #dc2626;
            background: rgba(220, 38, 38, 0.05);
        }

    .icon-logout::before {
        content: '→';
        transform: rotate(180deg);
        display: inline-block;
    }

    /* Mobile Responsive */
    @@media (max-width: 480px) {
        .login-container

    {
        padding: 16px;
    }

    .login-card, .user-profile-card {
        padding: 24px;
        border-radius: 16px;
    }

    .welcome-text h2 {
        font-size: 24px;
    }

    .teya-logo {
        flex-direction: column;
        gap: 8px;
    }

    .logo-text {
        font-size: 20px;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .user-details {
        text-align: center;
    }

    .security-features {
        gap: 8px;
    }

    .feature-item {
        padding: 10px;
    }

        .feature-item span {
            font-size: 13px;
        }

    }

    /* Dark mode support */
    @@media (prefers-color-scheme: dark) {
        .login-card, .user-profile-card

    {
        background: rgba(17, 24, 39, 0.95);
        border: 1px solid rgba(75, 85, 99, 0.3);
    }

    .welcome-text h2 {
        color: #f9fafb;
    }

    .welcome-text p {
        color: #d1d5db;
    }

    .user-name {
        color: #f9fafb;
    }

    .feature-item {
        background: rgba(102, 126, 234, 0.1);
        border-color: rgba(102, 126, 234, 0.2);
    }

        .feature-item span {
            color: #d1d5db;
        }

    .login-footer p {
        color: #9ca3af;
    }

    .privacy-text {
        color: #6b7280 !important;
    }

    }

    /* Accessibility improvements */
    @@media (prefers-reduced-motion: reduce) {
        *

    {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    }

    /* Focus styles for keyboard navigation */
    .btn:focus-visible {
        outline: 2px solid #667eea;
        outline-offset: 2px;
    }

    .support-link:focus-visible,
    .link:focus-visible {
        outline: 2px solid #667eea;
        outline-offset: 2px;
        border-radius: 4px;
    }
</style>
