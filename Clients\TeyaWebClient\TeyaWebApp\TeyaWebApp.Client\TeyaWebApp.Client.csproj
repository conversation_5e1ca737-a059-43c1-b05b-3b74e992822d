﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
    <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.0" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.40.0" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.18.2" />
    <PackageReference Include="MudBlazor" Version="7.8.0" />
    <PackageReference Include="MudBlazor.FontIcons.MaterialIcons" Version="1.2.0" />
    <PackageReference Include="Syncfusion.Blazor" Version="28.2.4" />
    <PackageReference Include="Syncfusion.Blazor.DropDowns" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Themes" Version="27.1.58" />
  </ItemGroup>

</Project>
