﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Graph.Models;
using Microsoft.Maui.Graphics;
using Syncfusion.Maui.Scheduler;
using System.Collections.ObjectModel;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

public partial class AppointmentsViewModel : ObservableObject
{
    private readonly AppointmentService _appointmentService;
    public ObservableCollection<Appointment> Appointments { get; set; } = new();
    public SchedulerAppointmentMapping AppointmentMapping { get; set; }

    public AppointmentsViewModel(AppointmentService appointmentService)
    {
        _appointmentService = appointmentService;

        AppointmentMapping = new SchedulerAppointmentMapping
        {
            Subject = "PatientName",
            StartTime = "StartTime",
            EndTime = "EndTime",
            Background = "Background"
        };

        LoadAppointmentsAsync().ConfigureAwait(false); 
    }

    private async Task LoadAppointmentsAsync()
    {
        try
        {
            var allAppointments = await _appointmentService.GetAllAppointmentsAsync();

            foreach (var appt in allAppointments)
            {
                DateTime startTime = appt.StartTime ?? appt.AppointmentDate;
                DateTime endTime = appt.EndTime ?? startTime.AddHours(1);

                string patientName = string.IsNullOrEmpty(appt.PatientName) ? "Unnamed Patient" : appt.PatientName;

                Appointments.Add(new Appointment
                {
                    PatientName = patientName,
                    StartTime = startTime,
                    EndTime = endTime,
                    Facility = appt.Facility,
                    Background = GetRandomColor()
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading appointments: {ex.Message}");
        }
    }

    private Brush GetRandomColor()
    {
        var colors = new[] { Colors.Blue, Colors.Green, Colors.Orange, Colors.Purple, Colors.Teal };
        var random = new Random();
        return new SolidColorBrush(colors[random.Next(colors.Length)]);
    }
}