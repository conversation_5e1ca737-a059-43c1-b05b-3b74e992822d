﻿using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileModel.ViewModel;


namespace TeyaWebApp.ViewModel
{
    public class OrganizationService : IOrganizationService
    {
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<OrganizationService> _localizer;
        private readonly ILogger<OrganizationService> _logger;
        

        public OrganizationService(HttpClient httpClient, IStringLocalizer<OrganizationService> localizer, ILogger<OrganizationService> logger)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger ?? throw new ArgumentNullException(nameof(_logger));
            Env.Load();
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        public async Task<Organization> RegisterOrganizationsAsync(Organization organization)
        {
            try
            {
                var bodyContent = JsonSerializer.Serialize(organization);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/Organizations";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);

                    try
                    {
                        return JsonSerializer.Deserialize<Organization>(responseData);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, _localizer["JsonDeserializationError", ex.Message]);
                        throw;
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    _logger.LogError("Organization already exists with status code {StatusCode}", response.StatusCode);
                    throw new HttpRequestException(_localizer["OrganizationAlreadyExists"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Registration failed. Status Code: {response.StatusCode}, Reason: {response.ReasonPhrase}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RegistrationFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["OrganizationRegistrationError"]);
                throw;
            }
        }

        public async Task<Organization> GetOrganizationByIdAsync(Guid OrganizationId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations/{OrganizationId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    return JsonSerializer.Deserialize<Organization>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingOrganizationById"], OrganizationId);
                throw;
            }
        }

        public async Task<List<Organization>> GetAllOrganizationsAsync()
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                return JsonSerializer.Deserialize<List<Organization>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllOrganizations"]);
                throw;
            }
        }

        public async Task DeleteOrganizationByIdAsync(Guid OrganizationId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations/{OrganizationId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["OrganizationDeletedSuccessfully"], OrganizationId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"DeleteFaileStatusCode: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["OrganizationDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingOrganization"], OrganizationId);
                throw;
            }
        }

        public async Task UpdateOrganizationByIdAsync(Guid OrganizationId, Organization Organization)
        {
            if (Organization == null || Organization.OrganizationId != OrganizationId)
            {
                _logger.LogError(_localizer["InvalidOrganization"]);
                throw new ArgumentException(_localizer["InvalidOrganization"]);
            }

            try
            {
                var apiUrl = $"{_MemberService}/api/Organizations/{OrganizationId}";
                var bodyContent = JsonSerializer.Serialize(Organization);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Content = content;
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["UpdateSuccessful"], OrganizationId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingOrganization"], OrganizationId);
                throw;
            }
        }

        public async Task<List<Organization>> GetOrganizationsByNameAsync(string name)
        {
            List<Organization> organizations = new List<Organization>();
            try
            {
                var response = await _httpClient.GetAsync($"{_MemberService}/api/Organizations/search/?name={Uri.EscapeDataString(name)}");
                response.EnsureSuccessStatusCode();
                organizations = await response.Content.ReadFromJsonAsync<List<Organization>>() ?? new List<Organization>();
            }
            catch (HttpRequestException httpEx)
            {
                _logger.LogError(httpEx, _localizer["ErrorMakingHttpRequestToGetOrganizations"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingOrganizations"]);
            }
            return organizations;
        }

        /// <summary>
        /// Retrieves the OrganizationId for a given organization name.
        /// </summary>
        /// <param name="orgName">The name of the organization.</param>
        /// <returns>The OrganizationId if found.</returns>
        /// <exception cref="KeyNotFoundException">Thrown when no organization is found with the given name.</exception>
        public async Task<Guid> GetOrganizationIdByNameAsync(string orgName)
        {
            var organization = (await GetOrganizationsByNameAsync(orgName))
                               ?.FirstOrDefault(org => org.OrganizationName == orgName);
            if (organization == null)
                throw new KeyNotFoundException(_localizer["OrganizationNotFound", orgName]);
            return organization.OrganizationId;
        }
    }
}
