﻿@page "/UnlicensedAccess"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [AllowAnonymous]
@using Microsoft.Extensions.Localization
@using MudBlazor
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject NavigationManager NavigationManager

<MudContainer>
    <MudPaper Elevation="3" Class="pa-6 text-center">
        <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Large" Color="Color.Error" />
        <MudText Typo="Typo.h4" Class="mt-4">@Localizer["Access Denied"]</MudText>
        <MudText Typo="Typo.body1" Class="mt-2">
            @Localizer["Your organization does not have an active license to use this software. Please contact support or acquire a license to gain access."]
        </MudText>

        <MudButton OnClick="NavigateToContact" Color="Color.Primary" Class="mt-4">
            @Localizer["Contact Support"]
        </MudButton>
    </MudPaper>
</MudContainer>


