﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Plugin.Maui.Audio;

public class AudioPlayerService
{
    private readonly IAudioManager _audioManager;

    public AudioPlayerService(IAudioManager audioManager)
    {
        _audioManager = audioManager;
    }

    public async Task PlayAudioAsync(string filePath)
    {
        if (!File.Exists(filePath))
        {
            Console.WriteLine("Audio file does not exist.");
            return;
        }

        var audioPlayer = _audioManager.CreatePlayer(await FileSystem.OpenAppPackageFileAsync(filePath));
        audioPlayer.Play();
    }
}
