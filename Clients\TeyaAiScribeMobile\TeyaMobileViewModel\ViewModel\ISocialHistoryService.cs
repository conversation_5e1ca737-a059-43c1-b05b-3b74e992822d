﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface ISocialHistoryService
    {
        Task<List<PatientSocialHistory>> GetAllByPatientIdAsync(Guid id);
        Task<List<PatientSocialHistory>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddHistoryAsync(PatientSocialHistory history);
        Task AddHistoryListAsync(List<PatientSocialHistory> histories);
        Task DeleteHistoryAsync(PatientSocialHistory history);
        Task DeleteHistoryListAsync(List<PatientSocialHistory> historyList);
        Task UpdateHistoryAsync(PatientSocialHistory history);
        Task UpdateHistoryListAsync(List<PatientSocialHistory> histories);

    }
}
