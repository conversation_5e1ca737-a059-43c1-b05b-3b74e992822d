﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICustomImmunizationAlertService
    {
        Task<List<CustomImmunizationAlerts>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<CustomImmunizationAlerts>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddCustomImmunizationAlertsAsync(List<CustomImmunizationAlerts> customimmunizationAlerts, Guid? OrgID, bool Subscription);
        Task UpdateCustomImmunizationAlertAsync(CustomImmunizationAlerts customimmunizationAlert, Guid? OrgID, bool Subscription);
        Task UpdateCustomImmunizationAlertsListAsync(List<CustomImmunizationAlerts> customimmunizationAlerts, Guid? OrgID, bool Subscription);
        Task DeleteCustomImmunizationAlertByEntityAsync(CustomImmunizationAlerts customimmunizationAlert, Guid? OrgID, bool Subscription);
    }
}