using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Navigations;
using System.IO;
using System.Reflection.Metadata;
using System.Text.Json;
using System.Xml.Linq;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using static Azure.Core.HttpHeader;
using static Microsoft.Graph.CoreConstants;
using static MudBlazor.CategoryTypes;
using static MudBlazor.Colors;
using static MudBlazor.Icons;
using static System.Net.Mime.MediaTypeNames;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace TeyaWebApp.Components.Pages
{
    public partial class Templates : Microsoft.AspNetCore.Components.ComponentBase
    {
        private bool[] ExpandedSections { get; set; } = new bool[0];

        private bool isAddSectionVisible = false;
        private bool _overrideStyles;
        private List<TemplateData> AllProviderData = new();
        private List<TemplateData> ProviderData = new();
        private bool Subscription = false;
        private TemplateData EditingTemplate;
        private string oldTemplateName;
        private SfListBox<string[], TemplateFieldModel> listBoxObj;
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        private List<TemplateFieldModel> templatefields = new();
        private string templateName;
        private List<TemplateSection> TemplateSections { get; set; } = new List<TemplateSection>();
        private List<VisitType> VisitTypes { get; set; }
        private string? SelectedVisitType { get; set; }
        private Guid OrgID { get; set; }

        public string DropDownValue { get; set; }
        public List<string> data = new();


        private void ResetSelection()
        {
            DropDownValue = null; // Reset to show placeholder
            ProviderData = AllProviderData;
            StateHasChanged();
        }

        private void OnValueSelecthandler(SelectEventArgs<string> args)
        {
            ProviderData = AllProviderData.Where(p => p.VisitType == args.ItemData).ToList();
            StateHasChanged();
        }

        protected override async Task OnInitializedAsync()
        {
            OrgID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrgID);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            await LoadVisitTypes();
            await LoadTemplates();
            data = VisitTypes.Select(v => v.VisitName).ToList();
            //var defaultTemplate = ProviderData.FirstOrDefault(t => t.IsDefault);
            //if (defaultTemplate != null)
            //{
            //    EditTemplate(defaultTemplate);
            //}
        }

        private void RowSelected(RowSelectEventArgs<TemplateData> args)
        {
            // Get the selected data item
            if (args.Data != null)
            {
                // Call EditTemplate with the selected data
                EditTemplate(args.Data);
            }
        }

        private void OnVisitTypeChange(string? newVisitType)
        {
            SelectedVisitType = newVisitType;
        }

        private async Task LoadVisitTypes()
        {
            try
            {
                var member = await MemberService.GetMemberByIdAsync(Guid.Parse(User.id),OrgID, Subscription);
                VisitTypes = await VisitTypeService.GetVisitTypesByOrganizationIdAsync(OrgID,Subscription);
                //VisitTypes = await VisitTypeService.GetAllVisitTypesAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
            }
        }

        private void ToggleCardVisibility()
        {
            ResetEditingState();
            isAddSectionVisible = true;
        }

        private void SetSectionStyle(string style, int index, int index2)
        {
            TemplateSections[index2].Fields[index].SectionStyle = style;
            templatefields[index].SectionStyle = style;
            StateHasChanged();
        }

        private Variant GetButtonVariant(string style, int index, int index2)
        {

            var value=TemplateSections[index2].Fields[index].SectionStyle == style ? Variant.Filled : Variant.Outlined;
            return value;
        }

       
        private async Task LoadTemplates()
        {
            try
            {
                AllProviderData = await TemplateService.GetTemplatesByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
                
                var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();

                // For new user to get predefined templates
                if (AllProviderData == null)
                {
                    foreach (var visitType in VisitTypes)
                    {
                            TemplateData newTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == visitType.VisitName);
                            if (newTemplate != null)
                            {
                                newTemplate.Id = Guid.NewGuid();
                                newTemplate.CreatedDate = DateTime.Now;
                                newTemplate.PCPId = Guid.Parse(User.id);
                                await TemplateService.CreateTemplatesAsync(newTemplate, OrgID, Subscription);

                        }
                    }
                    AllProviderData = await TemplateService.GetTemplatesByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
                }

                //For cases when we add new predefined templates in future
                foreach (var visitType in VisitTypes)
                {
                    var DefaultTemplateForVisitType = AllProviderData.Where(t => t.VisitType == visitType.VisitName && t.IsDefault == true);
                    if (!DefaultTemplateForVisitType.Any())
                    {

                        TemplateData newTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == visitType.VisitName);
                        if(newTemplate != null)
                        {
                            newTemplate.Id = Guid.NewGuid();
                            newTemplate.CreatedDate = DateTime.Now;
                            newTemplate.PCPId = Guid.Parse(User.id);
                            await TemplateService.CreateTemplatesAsync(newTemplate, OrgID, Subscription);
                            AllProviderData.Add(newTemplate);
                        }
                    }
                }
                ProviderData = AllProviderData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error"]);
            }

            StateHasChanged();
        }

        private async Task LoadPredefinedTemplates()
        {
            var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
            foreach (var template in predefinedTemplates)
            {
                template.Id= Guid.NewGuid();
                template.CreatedDate = DateTime.Now;
                template.PCPId = Guid.Parse(User.id);
                await TemplateService.CreateTemplatesAsync(template, OrgID, Subscription);
            }
        }
        private void AddTextBox(int sectionIndex)
        {
            var newField = new TemplateFieldModel
            {
                FieldName = "New Section",
                Instructions = string.Empty,
                SectionStyle = "Auto"
            };

            if (!TemplateSections.Any())
            {
                TemplateSections.Add(new TemplateSection
                {
                    SectionName = "New Section",
                    Fields = new List<TemplateFieldModel> { newField }
                });
            }
            else
            {
                TemplateSections[sectionIndex].Fields.Add(newField);
            }

            templatefields.Add(newField);
            StateHasChanged();
        }

        private void RemoveTextBox(int fieldIndex, int sectionIndex)
        {
            if (sectionIndex < TemplateSections.Count &&
                fieldIndex < TemplateSections[sectionIndex].Fields.Count)
            {
                TemplateSections[sectionIndex].Fields.RemoveAt(fieldIndex);
                templatefields.RemoveAt(fieldIndex);
                StateHasChanged();
            }
        }
        private void AddSectionHeader()
        {

            var newSection = new TemplateSection
            {
                SectionName = $"New Header {TemplateSections.Count + 1}",
                Fields =
                [new()
                 {
                    FieldName = "New Section",
                    Instructions = string.Empty,
                    SectionStyle = "Auto"
                 }
                ]
            };

            TemplateSections.Add(newSection);
            StateHasChanged();
        }
        private void DeleteSection(int sectionIndex)
        {
            if (sectionIndex < TemplateSections.Count)
            {
                var fieldsToRemove = TemplateSections[sectionIndex].Fields;
                templatefields.RemoveAll(f => fieldsToRemove.Contains(f));
                TemplateSections.RemoveAt(sectionIndex);
                StateHasChanged();
            }
        }
        private async Task OnIsDefaultChange(Microsoft.AspNetCore.Components.ChangeEventArgs args, TemplateData templateData)
        {
            if (args?.Value is bool isChecked && isChecked)
            {
                if (DropDownValue == null)
                {
                    foreach (var item in ProviderData)
                    {
                        if (item.VisitType == templateData.VisitType)
                        {
                            item.IsDefault = (item.Id == templateData.Id);
                        }
                        
                    }
                }
                else
                {
                    // Set only one template as default
                    foreach (var item in ProviderData)
                    {
                        item.IsDefault = (item.Id == templateData.Id);
                    }
                }
                await TemplateService.UpdateTemplatesAsync(ProviderData, OrgID, Subscription);
                StateHasChanged();
            }
        }

        private void ToggleSection(int sectionIndex)
        {
            TemplateSections[sectionIndex].IsExpanded = !TemplateSections[sectionIndex].IsExpanded;
        }


        private void EditTemplate(TemplateData templateData)
        {
            try
            {
                // Clear existing data
                SelectedVisitType = templateData.VisitType;
                EditingTemplate = templateData;
                oldTemplateName = templateData.TemplateName;
                templateName = templateData.TemplateName;
                templatefields = new List<TemplateFieldModel>();
                TemplateSections = new List<TemplateSection>();  // Reset the list instead of clearing

                if (string.IsNullOrEmpty(templateData.Template))
                {
                    return;
                }

                // Deserialize the JSON string directly into a dictionary
                var templateSectionsData = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, TemplateField>>>(templateData.Template);

                if (templateSectionsData != null)
                {
                    foreach (var section in templateSectionsData)
                    {
                        var sectionDisplay = new TemplateSection
                        {
                            SectionName = section.Key,
                            Fields = new List<TemplateFieldModel>() 
                        };

                        foreach (var field in section.Value)
                        {
                            var fieldModel = new TemplateFieldModel
                            {
                                FieldName = field.Key,
                                Instructions = field.Value.Instructions ?? string.Empty,  
                                SectionStyle = field.Value.SectionStyle ?? "Auto" 
                            };

                            sectionDisplay.Fields.Add(fieldModel);
                            templatefields.Add(fieldModel);
                        }

                        TemplateSections.Add(sectionDisplay);
                    }
                }

                isAddSectionVisible = true;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in EditTemplate");
                ResetEditingState();
            }
        }

        private void ShowDefaultTemplateInSectionSettings()
        {
            var defaultTemplate = ProviderData.FirstOrDefault(t => t.IsDefault);

            if (defaultTemplate != null)
            {
                templateName = defaultTemplate.TemplateName;

                templatefields.Clear();
                TemplateSections.Clear();

                if (!string.IsNullOrEmpty(defaultTemplate.Template))
                {
                    try
                    {
                        // Deserialize the JSON string directly into a dictionary
                        var templateSectionsData = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, TemplateField>>>(defaultTemplate.Template);

                        if (templateSectionsData != null)
                        {
                            foreach (var section in templateSectionsData)
                            {
                                var sectionDisplay = new TemplateSection
                                {
                                    SectionName = section.Key
                                };

                                foreach (var field in section.Value)
                                {
                                    sectionDisplay.Fields.Add(new TemplateFieldModel
                                    {
                                        FieldName = field.Key,
                                        Instructions = field.Value.Instructions,
                                        SectionStyle = field.Value.SectionStyle
                                    });

                                    templatefields.Add(new TemplateFieldModel
                                    {
                                        FieldName = field.Key,
                                        Instructions = field.Value.Instructions,
                                        SectionStyle = field.Value.SectionStyle
                                    });
                                }
                                TemplateSections.Add(sectionDisplay);
                            }
                        }
                    }
                    catch (JsonException ex)
                    {
                        Logger.LogError(ex, Localizer["ErrorShowingTemplate"]);
                    }
                }

                isAddSectionVisible = true;
            }
            else
            {
                ResetEditingState();
            }
        }



        private void ResetEditingState()
        {
            SelectedVisitType = null;
            EditingTemplate = null;
            oldTemplateName = null;
            templateName = string.Empty;
            templatefields.Clear();
            TemplateSections.Clear();
        }

        private async Task DeleteTemplate(TemplateData templateData)
        {
            if (templateData == null)
                return;

            try
            {
                await TemplateService.DeleteTemplatesAsync(templateData.Id, OrgID, Subscription);
                StateHasChanged();
                await LoadTemplates();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDeletingTemplate"]);
            }
        }
        private async Task SubmitForm()
        {
            try
            {
                bool isDefault = EditingTemplate?.IsDefault ?? false;
                var templateContent = new Dictionary<string, Dictionary<string, TemplateField>>();

                foreach (var sectionDisplay in TemplateSections)
                {
                    var sectionName = sectionDisplay.SectionName;
                    templateContent[sectionName] = new Dictionary<string, TemplateField>();
                    foreach (var field in sectionDisplay.Fields)
                    {
                        templateContent[sectionName][field.FieldName] = new TemplateField
                        {
                            Instructions = field.Instructions,
                            SectionStyle = field.SectionStyle
                        };
                    }
                }

                var jsonData = JsonSerializer.Serialize(templateContent);
                if (EditingTemplate != null)
                {
                    EditingTemplate.Template = jsonData;
                    EditingTemplate.TemplateName = templateName;
                    EditingTemplate.VisitType = SelectedVisitType;
                    await TemplateService.UpdateTemplatesAsync(new List<TemplateData> { EditingTemplate }, OrgID, Subscription);
                }
                else
                {
                    EditingTemplate = new TemplateData
                    {
                        Template = jsonData,
                        TemplateName = templateName,
                        IsDefault = false,
                        PCPId = Guid.Parse(User.id),
                        Id = Guid.NewGuid(),
                        CreatedDate = DateTime.Now,
                        VisitType= SelectedVisitType
                    };
                    await TemplateService.CreateTemplatesAsync(EditingTemplate, OrgID, Subscription);

                }
                AllProviderData = await TemplateService.GetTemplatesByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
                if (DropDownValue != null)
                {
                    ProviderData = AllProviderData.Where(p => p.VisitType == DropDownValue).ToList();
                }
                else
                {
                    ProviderData = AllProviderData;
                }
                //await LoadTemplates();
                isAddSectionVisible = false;
                ResetEditingState();
                //ShowDefaultTemplateInSectionSettings();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorSubmittingForm"]);
            }
        }
    }
}
