﻿@page "/Notes"
@using BusinessLayer.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using System.Text.RegularExpressions
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject IRoleService RoleService
@inject HttpClient Http
@using TeyaUIViewModels.ViewModel
@inject ISpeechService speechService
@inject IJSRuntime JSRuntime
@inject ITokenService TokenService
@inject IAddressService AddressService
@inject IInsuranceService InsuranceService
@inject IGuardianService GuardianService
@inject IEmployerService EmployerService
@inject IOrganizationService OrganizationService
@inject IProgressNotesService ProgressNotesService
@using System.Collections.Generic
@using Microsoft.AspNetCore.Components



@* @if (AICard)
{ *@



<MudGrid>
     @if (_PatientService.PatientData != null)
    {
        <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="() => AddNewRecord()" Class=" ml-7 mt-5 p-2">@Localizer["Add New Record"] </MudButton>
    }
    @if (PatientCreationFlag == true)
    {
        <MudItem xs="12" Class="mb-4">
            <MudPaper Style="background-color: #f5f5f5; border-radius: 8px; padding: 12px;">
                <MudGrid Class="align-center" Spacing="2">
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField T="string"
                                      Label="@Localizer["First Name"]"
                                      @bind-Value="member.FirstName"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField T="string"
                                      Label="@Localizer["Last Name"]"
                                      @bind-Value="member.LastName"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField T="DateTime?"
                                      Label="@Localizer["Date Of Birth"]"
                                      @bind-Value="member.DateOfBirth"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField T="string"
                                      Label="@Localizer["SSN"]"
                                      @bind-Value="member.SSN"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>
                </MudGrid>
                <MudGrid>
                    <MudItem xs="12" Class="mt-2">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Primary"
                                   OnClick="@CreatePatient"
                                   Style="height: 40px;">
                            @Localizer["Create Patient"]
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>
    }
    @for (int i = 0; i < records.Count; i++)
    {
        var record = records[i];
        
        <MudItem xs="12" Class="mx-auto">
            <MudCard Elevation="4" Class="mb-6 my-card">
                @if (productVisibility)
                {
                    <div class="button-container">
                        @if (record.Transcription == String.Empty)
                        {
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Primary"
                                       OnClick="() => ShowMicrophone(record)"
                                       Class="action-button"
                                       >
                                @Localizer["Teya AI"]
                            </MudButton>
                        }
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Primary"
                                   OnClick="OpenTreatmentDialogAsync"
                                   Class="action-button"
                                   >
                            @Localizer["Treatment"]
                        </MudButton>
                    </div>
                }
                <MudCardHeader Class="d-flex justify-space-between align-center">
                    <div>
                        <MudText Typo="Typo.h6">Patient Name : @record.PatientName</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">Record Date: @record.DateTime.ToString("g")</MudText>
                    </div>
                    @if (record.Transcription != String.Empty)
                    {
                        <audio controls>
                            <source src="@GetAudioUrl(record.Id)" type="audio/mp3" />
                            Your browser does not support the audio tag.
                        </audio>
                    }
                </MudCardHeader>
                <MudCardContent Class="p-4">
                    <div class="d-flex">
                        <!-- First Column: Notes -->
                        <div class="flex-fill me-2">
                            @foreach (var section in NotesData)
                            {
                                @foreach (var kvp in section)
                                {
                                    <p class="section-heading"><strong>@kvp.Key</strong></p>

                                    @foreach (var data in kvp.Value)
                                    {
                                        <MudCard Class="mt-3 pa-4">
                                            <p class="subsection-heading"><strong>@data.Key</strong></p>

                                            @if (ListDetails.Any(d => d.Name.Replace(" ", "").Equals(data.Key.Replace(" ", ""), StringComparison.OrdinalIgnoreCase)))
                                            {


                                                var recordNotes = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes);
                                                var TotalText = String.Empty;
                                                // Check if the keys exist in the deserialized data
                                                if (recordNotes != null &&
                                                recordNotes.ContainsKey(kvp.Key) &&
                                                recordNotes[kvp.Key].ContainsKey(data.Key))
                                                {
                                                    // Extract ONLY the manual content (before "Dynamic Content")
                                                    var fullContent = recordNotes[kvp.Key][data.Key];
                                                    TotalText = fullContent;
                                                    if (!string.IsNullOrEmpty(fullContent))
                                                    {
                                                        var manualPattern = @"<h4\s+style=['""]margin-top:\s*20px;\s*margin-bottom:\s*10px;['""]>Manual Content</h4>";
                                                        var dynamicPattern = @"<h4\s+style=['""]margin-bottom:\s*10px;['""]>Dynamic Content</h4>";

                                                        var manualMatch = Regex.Match(fullContent, manualPattern);
                                                        var dynamicMatch = Regex.Match(fullContent, dynamicPattern);

                                                        if (manualMatch.Success && dynamicMatch.Success && manualMatch.Index < dynamicMatch.Index)
                                                        {
                                                            int start = manualMatch.Index + manualMatch.Length;
                                                            int end = dynamicMatch.Index;
                                                            data_value = fullContent.Substring(start, end - start).Trim();
                                                        }
                                                        // Case 2: Only "Manual Content" exists -> Take everything after it
                                                        else if (manualMatch.Success)
                                                        {
                                                            data_value = fullContent.Substring(manualMatch.Index + manualMatch.Length).Trim();
                                                        }
                                                        // Case 3: Only "Dynamic Content" exists -> Take everything before it
                                                        else if (dynamicMatch.Success)
                                                        {
                                                            data_value = fullContent.Substring(0, dynamicMatch.Index).Trim();
                                                        }
                                                        // Case 4: Neither header exists -> Use fullContent
                                                        else
                                                        {
                                                            data_value = fullContent.Trim();
                                                        }
                                                    }
                                                    else
                                                    {
                                                        data_value = String.Empty;
                                                    }
                                                }

                                                var parameters = new Dictionary<string, object>
                            {
                            { "PatientID", record.PatientId },
                            { "Data", data_value },
                            { "OrgId",record.OrganizationId },
                            {"TotalText",TotalText},
                            { "OnValueChanged", EventCallback.Factory.Create<string>(this, async (updatedValue) =>
                            {
                            // Update the specific field in the record
                            var notesDict = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes)
                            ?? new Dictionary<string, Dictionary<string, string>>();

                            if (!notesDict.ContainsKey(kvp.Key))
                            notesDict[kvp.Key] = new Dictionary<string, string>();

                            notesDict[kvp.Key][data.Key] = updatedValue;
                            TotalText=updatedValue;
                            record.Notes = JsonSerializer.Serialize(notesDict);
                            await InvokeAsync(StateHasChanged);
                            })
                            }};
                                                var componentType = GetComponentType(data.Key.Replace(" ", ""));
                                                if (componentType != null)
                                                {
                                                    <DynamicComponent Type="componentType" Parameters="parameters" />
                                                }
                                                else
                                                {
                                                    <SfRichTextEditor Value="@GetEditorContent(record, kvp.Key, data.Key)"
                                                                      ValueChanged="@((string newValue) =>
                      HandleRichTextChange(record, kvp.Key, data.Key,newValue))">
                                                        <RichTextEditorToolbarSettings Items="@Tools">
                                                        </RichTextEditorToolbarSettings>
                                                    </SfRichTextEditor>
                                                }
                                            }
                                            else
                                            {
                                                <SfRichTextEditor Value="@GetEditorContent(record, kvp.Key, data.Key)"
                                                                  ValueChanged="@((string newValue) =>
                      HandleRichTextChange(record, kvp.Key, data.Key,newValue))">
                                                    <RichTextEditorToolbarSettings Items="@Tools">
                                                    </RichTextEditorToolbarSettings>
                                                </SfRichTextEditor>
                                            }
                                        </MudCard>
                                    }
                                }
                            }


                        </div>
                        @if (record.Transcription != String.Empty)
                        {
                            <!-- Second Column: Transcription -->

                            <div class="second-column ms-auto" style="width: 40%;">
                                <MudText Typo="Typo.subtitle2" Class="mb-2" Style="font-size : 18px">
                                    @Localizer["Transcription"]
                                </MudText>
                                <MudPaper Class="pa-4">
                                    <div class="conversation-container">
                                        @if (!string.IsNullOrEmpty(record.Transcription))
                                        {
                                            var lines = record.Transcription.Split('\n');
                                            string currentSpeaker = "";
                                            Dictionary<string, string> speakerColors = new Dictionary<string, string>();
                                            Dictionary<string, int> speakerPositions = new Dictionary<string, int>();
                                            int speakerCount = 0;

                                            @foreach (var line in lines)
                                            {
                                                if (string.IsNullOrWhiteSpace(line))
                                                {
                                                    continue;
                                                }

                                                // Check if line starts with speaker identifier
                                                var parts = line.Split(new[] { ":" }, 2, StringSplitOptions.None);
                                                if (parts.Length == 2)
                                                {
                                                    var speaker = parts[0].Trim();
                                                    var message = parts[1].Trim();
                                                    if (speaker.Equals("Unknown", StringComparison.OrdinalIgnoreCase))
                                                    {
                                                        continue;
                                                    }
                                                    currentSpeaker = speaker;

                                                    // Assign color and position if this is a new speaker
                                                    if (!speakerColors.ContainsKey(speaker))
                                                    {
                                                        speakerColors[speaker] = GetSpeakerColor(speakerCount);
                                                        speakerPositions[speaker] = speakerCount % 2; // 0 = left, 1 = right
                                                        speakerCount++;
                                                    }

                                                    var alignment = speakerPositions[speaker] == 0 ? "left-align" : "right-align";
                                                    string speakerColor = speakerColors[speaker];
                                                    string bubbleColor = GetBubbleColor(speakerColor);

                                                    <div class="conversation-message @alignment">
                                                        <div class="speaker-name-container" style=@($"color: {speakerColor}")>
                                                            <MudText Typo="Typo.subtitle2" Class="speaker-name">@speaker</MudText>
                                                        </div>
                                                        <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                                            <MudText Style="text-align: left;">@message</MudText>
                                                        </MudPaper>
                                                    </div>
                                                }
                                                else
                                                {
                                                    // If no speaker identified, use the current speaker's alignment
                                                    if (!string.IsNullOrEmpty(currentSpeaker) && speakerPositions.ContainsKey(currentSpeaker))
                                                    {
                                                        var alignment = speakerPositions[currentSpeaker] == 0 ? "left-align" : "right-align";
                                                        string bubbleColor = GetBubbleColor(speakerColors[currentSpeaker]);

                                                        <div class="conversation-message @alignment">
                                                            <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                                                <MudText Style="text-align: left;">@line</MudText>
                                                            </MudPaper>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        // Fallback for lines without a speaker context
                                                        <div class="conversation-message left-align">
                                                            <MudPaper Elevation="0" Class="message-bubble" Style="background-color: #f5f5f5">
                                                                <MudText Style="text-align: left;">@line</MudText>
                                                            </MudPaper>
                                                        </div>
                                                    }
                                                }
                                            }
                                        }
                                    </div>
                                </MudPaper>
                            </div>
                        }
                    </div>
                </MudCardContent>

                @if (record.isEditable)
                {
                    <div class="d-flex justify-end mt-4 mb-2 me-6">
                        <MudButton Color="Color.Primary" Variant="Variant.Filled" Class="uniform-button me-2" OnClick="@(async () => await SaveRecord(record))">
                            Save
                        </MudButton>

                        <MudButton Color="Color.Primary" Variant="Variant.Outlined" Class="uniform-button me-2" OnClick="@(async () => await LockRecord(record))">
                            Lock <MudIcon Icon="@Icons.Material.Filled.Lock" Class="ml-1" />
                        </MudButton>
                    </div>

                }
            
            </MudCard>

        </MudItem>
    }
</MudGrid>

<MudDialog Class="recording-dialog" @ref="MicrophoneDialog" OnBackdropClick="HandleBackdropClick"
           Style="width: 500px; border-radius: 12px; overflow: hidden; background: white;">
    <DialogContent>
        <div class="dialog-header">
            <MudIconButton Icon="@Icons.Material.Filled.Close" Color="Color.Error" Size="Size.Small" OnClick="CloseMicrophoneDialog" />
        </div>
        <div class="recording-container">
            @if (isLoading)
            {
                <!-- Processing state -->
                <div class="processing-indicator">
                    <span class="material-icons spin">@Localizer["autorenew"]</span>
                    <div class="processing-text">@Localizer["Processing..."]</div>
                </div>
            }
            else if (isRecorderActive)
            {
                <!-- Recording state -->
                <div class="recording-indicator-start" style="@(isPaused ? "background-color: #2bcbba;" : "")">
                    @(isPaused ? "Paused" : "Recording...")
                </div>
                <div class="wave-container">
                    <div class="wave-group">
                        @for (int i = 0; i < 40; i++)
                        {
                            <div class="wave-bar" style="@GetBarStyle(i)"></div>
                        }
                    </div>
                </div>
                <div>
                    <span class="timer-recording">@FormatCallDuration(callDuration)</span>
                </div>
                <div class="controls-wrapper">
                    <MudButton StartIcon="@(isPaused ? Icons.Material.Filled.PlayArrow : Icons.Material.Filled.Pause)"
                               OnClick="OnPauseIconClick"
                               Class="end-visit-btn">
                        @Localizer[isPaused ? "Resume" : "Pause"]
                    </MudButton>

                    <MudButton Class="end-visit-btn"
                               OnClick="OnMicIconClick"
                               StartIcon="@Icons.Material.Filled.StopCircle">
                        @Localizer["Stop"]
                    </MudButton>
                </div>
            }
            else
            {
                <!-- Initial state -->
                <div class="recording-indicator" style="background-color: red;">
                    @Localizer["Teya Ai Scribe"]
                </div>
                <div class="wave-container">
                    <div class="wave-group">
                        @for (int i = 0; i < 40; i++)
                        {
                            <div class="wave-bar" style="@GetNormalBarStyle()"></div>
                        }
                    </div>
                </div>
                <div>
                    <span class="timer">@FormatCallDuration(callDuration)</span>
                </div>
                <div class="controls-wrapper">
                    <MudButton Class="start-visit-btn"
                               OnClick="OnMicIconClick"
                               StartIcon="@Icons.Material.Filled.Mic">
                        @Localizer["Start"]
                    </MudButton>
                </div>
            }
        </div>
    </DialogContent>
</MudDialog>

<style>
    .dialog-header {
        display: flex;
        justify-content: flex-end;
    }

    .full-width {
        width: 100%;
    }

    .align-center {
        display: flex;
        align-items: center;
    }

    /* For better spacing between items on small screens */
    @@media (max-width: 600px) {
        .pr-sm-4 {
            padding-right: 0 !important;
        }
    }

    .my-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background-color: rgba(235, 225, 216, 1);
        width: 100%;
    }

        .my-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

    .first-column {
        display: flex;
        flex-direction: column;
    }

    .second-column {
        width: 400px;
        min-width: 300px;
    }

    .mud-card-content {
        padding: 16px;
    }

    .conversation-container {
        display: flex;
        flex-direction: column;
        gap: 6px;
        max-height: 500px;
        overflow-y: auto;
        padding: 10px;
    }

    .conversation-message {
        display: flex;
        flex-direction: column;
        max-width: 80%;
    }

    .left-align {
        align-self: flex-start;
    }

    .right-align {
        align-self: flex-end;
    }

        .right-align .speaker-name-container {
            align-self: flex-end;
        }

        .right-align .message-bubble {
            margin-left: auto;
        }

    .speaker-name {
        margin-bottom: 4px;
        font-weight: bold;
    }

    .message-bubble {
        padding: 8px;
        border-radius: 12px;
        word-break: break-word;
    }

    /* Extra styling for better readability */
    .left-align .message-bubble {
        border-top-left-radius: 2px;
    }

    .right-align .message-bubble {
        border-top-right-radius: 2px;
    }

    .custom-rte-container {
        position: relative;
        transition: margin-top 0.3s ease;
    }

        .custom-rte-container .e-rte-toolbar {
            display: none;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            border-radius: 4px;
            margin-bottom: 0;
        }

        .custom-rte-container:hover .e-rte-toolbar {
            display: block;
        }

        .custom-rte-container .e-rte-toolbar-wrapper {
            background-color: white;
            border-radius: 4px;
        }

    .button-container {
        display: flex;
        gap: 8px;
        padding: 8px;
        background-color: var(--mud-palette-background-grey);
        border-radius: var(--mud-default-borderradius);
        margin: 8px;
        width: fit-content;
    }

    .action-button {
        min-width: 120px;
        height: 36px;
        margin: 0 !important;
        border-radius: var(--mud-default-borderradius);
        transition: all 0.2s ease;
    }

        .action-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--mud-elevation-1);
        }

    /* For small screens */
    @@media (max-width: 600px) {
        .button-container {
            flex-direction: row;
            width: auto;
        }

        .action-button {
            min-width: 100px;
            font-size: 0.8rem;
        }
    }

.uniform-button {
    min-width: 95px; /* Adjust as needed */
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

</style>