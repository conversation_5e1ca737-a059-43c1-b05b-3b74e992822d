﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>TeyaAiScribeMobile</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>TeyaAiScribeMobile</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.teyaaiscribemobile</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
		<WindowsPackageType>None</WindowsPackageType>
		
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">26.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>

		<AndroidMinSdkVersion>26</AndroidMinSdkVersion>
   		<AndroidTargetSdkVersion>34</AndroidTargetSdkVersion>
   		<NeutralLanguage>en-US</NeutralLanguage>
		
		<EnableStaticWebAssets>false</EnableStaticWebAssets>
	</PropertyGroup>


	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
	  <AndroidResource Remove="Resources\GlobalFiles\**" />
	  <AndroidResource Remove="TeyaMobileModel\**" />
	  <AndroidResource Remove="TeyaMobileViewModel\**" />
	  <Compile Remove="Resources\GlobalFiles\**" />
	  <Compile Remove="TeyaMobileModel\**" />
	  <Compile Remove="TeyaMobileViewModel\**" />
	  <EmbeddedResource Remove="Resources\GlobalFiles\**" />
	  <EmbeddedResource Remove="TeyaMobileModel\**" />
	  <EmbeddedResource Remove="TeyaMobileViewModel\**" />
	  <MauiCss Remove="Resources\GlobalFiles\**" />
	  <MauiCss Remove="TeyaMobileModel\**" />
	  <MauiCss Remove="TeyaMobileViewModel\**" />
	  <MauiXaml Remove="Resources\GlobalFiles\**" />
	  <MauiXaml Remove="TeyaMobileModel\**" />
	  <MauiXaml Remove="TeyaMobileViewModel\**" />
	  <None Remove="Resources\GlobalFiles\**" />
	  <None Remove="TeyaMobileModel\**" />
	  <None Remove="TeyaMobileViewModel\**" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="AppointmentService.cs" />
	  <Compile Remove="Resources\raw\TeyaAIScribeResource.Designer.cs" />
	  <Compile Remove="Resources\raw\TeyaAIScribeStrings.Designer.cs" />
	  <Compile Remove="Resources\raw\TeyaUIViewModelsResource.Designer.cs" />
	  <Compile Remove="Resources\raw\TeyaUIViewModelsStrings.Designer.cs" />
	</ItemGroup>

	<ItemGroup>
	  <MauiAsset Remove="Resources\Raw\TeyaAIScribeResource.resx" />
	  <MauiAsset Remove="Resources\Raw\TeyaAIScribeStrings.resx" />
	  <MauiAsset Remove="Resources\Raw\TeyaUIViewModelsResource.Designer.cs" />
	  <MauiAsset Remove="Resources\Raw\TeyaUIViewModelsResource.resx" />
	  <MauiAsset Remove="Resources\Raw\TeyaUIViewModelsStrings.Designer.cs" />
	  <MauiAsset Remove="Resources\Raw\TeyaUIViewModelsStrings.resx" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="appsettings.json" />
	  <None Remove="Resources\Fonts\MaterialIcons-Regular.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Regular.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Semibold.ttf" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Resources\Raw\TeyaUIViewModelsResource.Designer.cs" />
	  <EmbeddedResource Include="Resources\Raw\TeyaUIViewModelsStrings.Designer.cs" />
	</ItemGroup>

	<ItemGroup>
	  <MauiAsset Include="appsettings.json">
	  </MauiAsset>
	</ItemGroup>

	<ItemGroup>
	  <MauiFont Include="Resources\Fonts\MaterialIcons-Regular.ttf" />
	  <MauiFont Include="Resources\Fonts\OpenSans-Regular.ttf" />
	  <MauiFont Include="Resources\Fonts\OpenSans-Semibold.ttf" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
		<PackageReference Include="Azure.ResourceManager.CognitiveServices" Version="1.4.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
		<PackageReference Include="CommunityToolkit.Maui" Version="11.1.0" />
		<PackageReference Include="CommunityToolkit.Maui.Core" Version="11.1.0" />
		<PackageReference Include="CommunityToolkit.Maui.MediaElement" Version="6.0.1" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="DotNetEnv" Version="3.1.1" />
		<PackageReference Include="Markdig" Version="0.41.1" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Extensions" Version="2.3.0" />
		<PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.43.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.3" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.3" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.3" />
		<PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.3" />
		<PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="9.0.3" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.70.0" />
		<PackageReference Include="Microsoft.JSInterop" Version="9.0.3" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.51" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.3" />
		<PackageReference Include="Microsoft.Maui.Essentials" Version="9.0.51" />
		<PackageReference Include="Microsoft.Net.Http" Version="2.2.29" />
		<PackageReference Include="Microsoft.Net.Http.Headers" Version="9.0.4" />
		<PackageReference Include="MKFilePicker" Version="1.0.3" />
		<PackageReference Include="Plugin.Maui.Audio" Version="3.1.0" />
		<PackageReference Include="Syncfusion.Blazor.Core" Version="29.1.39" />
		<PackageReference Include="Syncfusion.JavaScript" Version="22.1.34" />
		<PackageReference Include="Syncfusion.Maui.Buttons" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.Cards" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.Carousel" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.Core" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.DataGridExport" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.Inputs" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.ListView" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.Popup" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.Scheduler" Version="28.2.12" />
		<PackageReference Include="Syncfusion.Maui.Toolkit" Version="1.0.4" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\TeyaMobileModel\TeyaMobileModel.csproj" />
	  <ProjectReference Include="..\TeyaMobileViewModel\TeyaMobileViewModel.csproj" />
	</ItemGroup>

	<ItemGroup>
		<TrimmerRootAssembly Include="Microsoft.Extensions.Localization.Abstractions" />
		<TrimmerRootAssembly Include="Microsoft.AspNetCore.Http.Abstractions" />
		<TrimmerRootAssembly Include="Microsoft.AspNetCore.Http" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="AppointmentsDoctor.xaml.cs">
	    <DependentUpon>AppointmentsDoctor.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="SignIn.xaml.cs">
	    <DependentUpon>SignIn.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="TeyaAiScribeMobileResource\TeyaAiScribeMobileRes.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>TeyaAiScribeMobileRes.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="TeyaAiScribeMobileResource\TeyaAiScribeMobileRes.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>TeyaAiScribeMobileRes.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="MainPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Message.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="AppointmentsDoctor.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="PatientHome.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="ProviderHome.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="TemplatesPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="TeyaAI.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="UserSettings.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

	<ItemGroup Condition="'$(TargetFramework)' == 'net9.0-ios'">
	  <PackageReference Include="SpeakLink">
	    <Version>0.6.10</Version>
	  </PackageReference>
	</ItemGroup>

	<ItemGroup Condition="'$(TargetFramework)' == 'net9.0-maccatalyst'">
	  <PackageReference Include="SpeakLink">
	    <Version>0.6.10</Version>
	  </PackageReference>
	</ItemGroup>

</Project>
