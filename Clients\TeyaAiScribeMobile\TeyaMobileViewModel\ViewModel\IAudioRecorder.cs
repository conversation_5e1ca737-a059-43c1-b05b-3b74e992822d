﻿using Microsoft.Graph.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IAudioRecorder
    {
        /// <summary>
        /// Sets the file name (without extension) or ID for the next recording.
        /// </summary>
        void SetNextRecordingId(Guid recordingId);

        /// <summary>
        /// Current state of the recording
        /// </summary>
        RecordingState RecordingState { get; }

        /// <summary>
        /// Event triggered when recording state changes
        /// </summary>
        event EventHandler<RecordingState> RecordingStateChanged;

        /// <summary>
        /// Event triggered when an error occurs during recording
        /// </summary>
        event EventHandler<Exception> ErrorOccurred;

        /// <summary>
        /// Starts a new audio recording
        /// </summary>
        /// <returns>Task representing the asynchronous operation</returns>
        Task StartRecordingAsync();

        /// <summary>
        /// Pauses the current recording (if supported by the platform)
        /// </summary>
        /// <returns>Task representing the asynchronous operation</returns>
        Task PauseRecordingAsync();

        /// <summary>
        /// Resumes a paused recording (if supported by the platform)
        /// </summary>
        /// <returns>Task representing the asynchronous operation</returns>
        Task ResumeRecordingAsync();

        /// <summary>
        /// Stops the current recording and returns the file path
        /// </summary>
        /// <returns>Path to the recorded audio file</returns>
        Task<string> StopRecordingAsync();

        /// <summary>
        /// Gets the current recording duration in seconds
        /// </summary>
        /// <returns>Duration in seconds</returns>
        double GetRecordingDuration();

        /// <summary>
        /// Checks if the platform supports pausing and resuming recordings
        /// </summary>
        /// <returns>True if pause/resume is supported, false otherwise</returns>
        bool SupportsPauseResume();

        /// <summary>
        /// Gets the file extension used by this recorder (e.g., ".m4a")
        /// </summary>
        /// <returns>File extension with dot prefix</returns>
        string GetFileExtension();
    }
}