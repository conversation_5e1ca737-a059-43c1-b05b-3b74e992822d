﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface ITemplateService
    {
        Task<List<TemplateData>> GetTemplatesAsync();
        Task<List<TemplateData>> GetTemplatesByIdAsync(Guid Id);
        Task CreateTemplatesAsync(TemplateData templates);
        Task DeleteTemplatesAsync(Guid templateId);
        Task<List<TemplateData>> GetTemplatesByPCPIdAsync(Guid PCPId);
        Task UpdateTemplatesAsync(List<TemplateData> templates);
    }
}