﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.TeyaUIViewModelResources;

namespace TeyaMobileViewModel.ViewModel
{
    public class AllergyService : IAllergyService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public AllergyService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all allergies data, both active and inactive
        /// </summary>
        public async Task<List<Allergy>> GetAllergyByIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/Allergies/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Allergy>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AllergyServiceError2"]);
            }
        }

        /// <summary>
        /// Get all active allergies data
        /// </summary>
        public async Task<List<Allergy>> GetAllergyByIdAsyncAndIsActive(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/Allergies/{id}/isActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Allergy>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AllergyServiceError2"]);
            }
        }

        /// <summary>
        /// Add new list of allergies
        /// </summary>
        public async Task AddAllergyAsync(List<Allergy> allergies)
        {
            var apiUrl = $"{_EncounterNotes}/api/Allergies/AddAllergy";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(allergies);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["AllergyServiceError1"]);
            }
        }

        /// <summary>
        /// Delete allergy data
        /// </summary>
        public async Task DeleteAllergyAsync(Allergy allergy)
        {
            var apiUrl = $"{_EncounterNotes}/api/Allergies";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(allergy);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["AllergyServiceError1"]);
            }
        }

        /// <summary>
        /// Update single allergy data
        /// </summary>
        /// <param name="allergy"></param>
        /// <returns></returns>
        public async Task UpdateAllergyAsync(Allergy allergy)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/Allergies/{allergy.PatientId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(allergy);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Update allergy list data
        /// </summary>
        public async Task UpdateAllergyListAsync(List<Allergy> newAllergies, List<Allergy> updatedAllergies, List<Allergy> deletedAllergies, Guid patientId)
        {
            var apiUrl = $"{_EncounterNotes}/api/Allergies/UpdateAllergyList";
            var accessToken = _tokenService.AccessToken;

            // Combine into a single payload
            var payload = new
            {
                PatientId = patientId,
                NewAllergies = newAllergies,
                UpdatedAllergies = updatedAllergies,
                DeletedAllergies = deletedAllergies
            };

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(payload);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(_localizer["AllergyServiceError1"]);
            }
        }
    }
}