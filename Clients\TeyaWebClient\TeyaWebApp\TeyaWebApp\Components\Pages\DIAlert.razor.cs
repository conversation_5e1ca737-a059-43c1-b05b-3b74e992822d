﻿using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;

namespace TeyaWebApp.Components.Pages
{
    public partial class DIAlert
    {
        [Inject] public IDiagnosticImagingAlertService DiagnosticImagingAlertService { get; set; }
        [Inject] private ILogger<DiagnosticImagingAlerts> _logger { get; set; }
        [Inject] private IStringLocalizer<DiagnosticImagingAlerts> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }

        [CascadingParameter] private MudDialogInstance? MudDialog { get; set; }

        private SfGrid<DiagnosticImagingAlerts>? DIAlertGrid;

        private List<DiagnosticImagingAlerts> diAlerts = new();
        private List<DiagnosticImagingAlerts> deleteAlertList = new();
        private List<DiagnosticImagingAlerts> addList = new();

        // Form fields
        private string alertName = string.Empty;
        private string alertDescription = string.Empty;
        private string webReference = string.Empty;
        private int? ageLowerBound;
        private int? ageUpperBound;
        private string orderSet = string.Empty;
        private string gender = "Both";

        [Inject]
        private PatientService PatientService { get; set; } = default!;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }

        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientService.PatientData.Id;
            OrgID = PatientService.PatientData.OrganizationID;
            try
            {
                await LoadAlertsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lab alert data");
            }
        }
        private async Task LoadAlertsAsync()
        {
            var existingAlerts = await DiagnosticImagingAlertService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, false);
            diAlerts = existingAlerts?.ToList() ?? new List<DiagnosticImagingAlerts>();
            int emptyRowsNeeded = 9 - diAlerts.Count;
            if (emptyRowsNeeded > 0)
            {
                diAlerts.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                    .Select(_ => new DiagnosticImagingAlerts
                    {
                        Name = string.Empty,
                        Description = string.Empty,
                        WebReference = string.Empty,
                        OrderSet = string.Empty,
                        Gender = string.Empty
                    }));
            }
        }
        public void ActionCompletedHandler(ActionEventArgs<DiagnosticImagingAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedAlert = args.Data;
                var existingItem = addList.FirstOrDefault(v => v.Id == deletedAlert.Id);

                if (existingItem != null)
                {
                    addList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;  // Set IsActive to false instead of actually deleting
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteAlertList.Add(args.Data);
                }
            }
        }
        public async Task ActionBeginHandler(ActionEventArgs<DiagnosticImagingAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }
        private async Task SaveData()
        {
            try
            {
                if (addList.Count > 0)
                {
                    // Set IsActive to true for all new alerts
                    foreach (var alert in addList)
                    {
                        alert.IsActive = true;
                    }
                    await DiagnosticImagingAlertService.AddDIAlertsAsync(addList, OrgID, false);
                }

                if (deleteAlertList.Count > 0)
                {
                    await DiagnosticImagingAlertService.UpdateDIAlertsListAsync(deleteAlertList, OrgID, false);
                }
                var existingAlerts = diAlerts.Where(a => !string.IsNullOrEmpty(a.Name) && a.Id != Guid.Empty).ToList();
                if (existingAlerts.Count > 0)
                {
                    await DiagnosticImagingAlertService.UpdateDIAlertsListAsync(existingAlerts, OrgID, false);
                }
                deleteAlertList.Clear();
                addList.Clear();

                await LoadAlertsAsync();
                ResetInputFields();

                Snackbar.Add(_localizer["DI alerts saved successfully"], Severity.Success);
                MudDialog?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving di alert data");
                Snackbar.Add(_localizer["Failed to save di alert records"], Severity.Error);
            }
        }
        private async Task CancelData()
        {
            deleteAlertList.Clear();
            addList.Clear();
            await LoadAlertsAsync();
            ResetInputFields();

            // Close the dialog
            MudDialog?.Close();
        }
        private void ResetInputFields()
        {
            alertName = string.Empty;
            alertDescription = string.Empty;
            webReference = string.Empty;
            ageLowerBound = null;
            ageUpperBound = null;
            orderSet = string.Empty;
            gender = "Both";
        }

        private async Task AddNewAlert()
        {
            try
            {
                if (string.IsNullOrEmpty(alertName))
                {
                    Snackbar.Add(_localizer["Please enter alert name"], Severity.Warning);
                    return;
                }

                var emptyRow = diAlerts.FirstOrDefault(i => string.IsNullOrEmpty(i.Name));

                if (emptyRow == null)
                {
                    emptyRow = new DiagnosticImagingAlerts();
                    diAlerts.Add(emptyRow);
                }

                emptyRow.Id = Guid.NewGuid();
                emptyRow.PatientId = PatientId;
                emptyRow.pcpId = Guid.Parse(User.id);
                emptyRow.OrganizationId = OrgID ?? Guid.Empty;
                emptyRow.CreatedDate = DateTime.Now;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.Name = alertName;
                emptyRow.Description = alertDescription;
                emptyRow.WebReference = webReference;
                emptyRow.AgeLowerBound = ageLowerBound;
                emptyRow.AgeUpperBound = ageUpperBound;
                emptyRow.OrderSet = orderSet;
                emptyRow.Gender = gender;
                emptyRow.IsActive = true;  // Set IsActive to true for new alerts

                addList.Add(emptyRow);

                if (DIAlertGrid != null)
                {
                    await DIAlertGrid.Refresh();
                }

                ResetInputFields();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new di alert");
                Snackbar.Add(_localizer["Failed to add di alert"], Severity.Error);
            }
        }
    }
}
