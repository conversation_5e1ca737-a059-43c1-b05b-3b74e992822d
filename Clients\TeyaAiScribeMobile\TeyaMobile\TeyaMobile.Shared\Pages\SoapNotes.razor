﻿@page "/soapnotes"
@page "/soapnotes/{PatientId:guid}"
@page "/soapnotes/{PatientId:guid}/{RecordingId:guid}"
@using TeyaMobileModel.Model
@using TeyaMobileViewModel.ViewModel
@using System.Text.Json
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Notifications
@inject IProgressNotesService NotesService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<PageTitle>SOAP Notes</PageTitle>

<div class="soap-notes-container">
    <!-- Header -->
    <div class="soap-header">
        <div class="header-content">
            <div class="back-section" @onclick="NavigateBack">
                <i class="e-icons e-chevron-left"></i>
                <span>Back</span>
            </div>
            <div class="title-section">
                <h3>SOAP Notes</h3>
                <span class="patient-info">@(!string.IsNullOrEmpty(currentPatientName) ? currentPatientName : $"Patient ID: {PatientId.ToString("N")[..8]}...")</span>
            </div>
            <div class="action-section">
                <Syncfusion.Blazor.Buttons.SfButton CssClass="save-btn"
                                                    OnClick="SaveAllNotes"
                                                    Disabled="@isSaving">
                    @if (isSaving)
                    {
                        <i class="e-icons e-refresh spinning"></i>
                    }
                    else
                    {
                        <i class="e-icons e-save"></i>
                    }
                    Save All
                </Syncfusion.Blazor.Buttons.SfButton>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <span>Loading SOAP Notes...</span>
        </div>
    }

    <!-- Content -->
    <div class="soap-content">
        @if (soapRecords.Any())
        {
            <!-- Dynamic SOAP Notes from Database -->
            @foreach (var record in soapRecords)
            {
                <div class="record-card">
                    <div class="record-header">
                        <div class="record-info">
                            <span class="record-date">@record.DateTime.ToString("MMM dd, yyyy HH:mm")</span>
                            <span class="patient-name">@record.PatientName</span>
                        </div>
                        <span class="record-status @(record.isEditable == true ? "editable" : "readonly")">
                            @(record.isEditable == true ? "Editable" : "Read Only")
                        </span>
                    </div>

                    @if (record.ParsedNotes != null)
                    {
                        @foreach (var soapSection in record.ParsedNotes)
                        {
                            <div class="soap-section">
                                <div class="section-header">
                                    <h4>@soapSection.Key</h4>
                                    <div class="section-actions">
                                        <Syncfusion.Blazor.Buttons.SfButton CssClass="expand-btn"
                                                                            IconCss="@(expandedSections.Contains($"{record.Id}_{soapSection.Key}") ? "e-icons e-chevron-up" : "e-icons e-chevron-down")"
                                                                            OnClick="() => ToggleSection(record.Id, soapSection.Key)">
                                        </Syncfusion.Blazor.Buttons.SfButton>
                                    </div>
                                </div>

                                <div class="section-content @(expandedSections.Contains($"{record.Id}_{soapSection.Key}") ? "expanded" : "collapsed")">
                                    @foreach (var item in soapSection.Value)
                                    {
                                        <div class="soap-field">
                                            <div class="field-header">
                                                <label class="field-label">@item.Key</label>
                                                @if (record.isEditable == true)
                                                {
                                                    <Syncfusion.Blazor.Buttons.SfButton CssClass="edit-btn"
                                                                                        IconCss="e-icons e-edit"
                                                                                        OnClick="() => ToggleEdit(record.Id, soapSection.Key, item.Key)">
                                                    </Syncfusion.Blazor.Buttons.SfButton>
                                                }
                                            </div>

                                            <div class="field-content">
                                                @{
                                                    var fieldKey = $"{record.Id}_{soapSection.Key}_{item.Key}";
                                                    var isEditing = editingFields.Contains(fieldKey);
                                                }

                                                @if (isEditing && record.isEditable == true)
                                                {
                                                    <div class="edit-container">
                                                        <Syncfusion.Blazor.Inputs.SfTextBox @ref="editTextBox"
                                                                                            Value="@item.Value"
                                                                                            ValueChanged="@((string value) => UpdateFieldValue(record.Id, soapSection.Key, item.Key, value))"
                                                                                            Multiline="true"
                                                                                            FloatLabelType="FloatLabelType.Never"
                                                                                            CssClass="edit-textbox">
                                                        </Syncfusion.Blazor.Inputs.SfTextBox>
                                                        <div class="edit-actions">
                                                            <Syncfusion.Blazor.Buttons.SfButton CssClass="save-field-btn"
                                                                                                OnClick="() => SaveField(record.Id, soapSection.Key, item.Key)">
                                                                Save
                                                            </Syncfusion.Blazor.Buttons.SfButton>
                                                            <Syncfusion.Blazor.Buttons.SfButton CssClass="cancel-btn"
                                                                                                OnClick="() => CancelEdit(record.Id, soapSection.Key, item.Key)">
                                                                Cancel
                                                            </Syncfusion.Blazor.Buttons.SfButton>
                                                        </div>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="field-display @(string.IsNullOrEmpty(item.Value) ? "empty" : "")">
                                                        @if (string.IsNullOrEmpty(item.Value))
                                                        {
                                                            <span class="placeholder-text">Click edit to add content...</span>
                                                        }
                                                        else
                                                        {
                                                            <pre class="field-text">@item.Value</pre>
                                                        }
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    }
                    else if (!string.IsNullOrEmpty(record.Notes))
                    {
                        <!-- Display raw notes if parsing fails -->
                        <div class="raw-notes-section">
                            <div class="section-header">
                                <h4>Notes</h4>
                            </div>
                            <div class="raw-notes-content">
                                <pre class="raw-notes-text">@record.Notes</pre>
                            </div>
                        </div>
                    }
                </div>
            }
        }
        else if (!isLoading)
        {
            <!-- Default SOAP Template when no data -->
            <div class="default-soap-template">
                <div class="template-header">
                    <h4>Create New SOAP Note</h4>
                    <span class="template-subtitle">No existing notes found. Use the template below to create a new SOAP note.</span>
                </div>

                @foreach (var templateSection in defaultSoapTemplate)
                {
                    <div class="soap-section">
                        <div class="section-header">
                            <h4>@templateSection.Key</h4>
                            <div class="section-actions">
                                <Syncfusion.Blazor.Buttons.SfButton CssClass="expand-btn"
                                                                    IconCss="@(expandedSections.Contains($"default_{templateSection.Key}") ? "e-icons e-chevron-up" : "e-icons e-chevron-down")"
                                                                    OnClick="() => ToggleSection(Guid.Empty, templateSection.Key, true)">
                                </Syncfusion.Blazor.Buttons.SfButton>
                            </div>
                        </div>

                        <div class="section-content @(expandedSections.Contains($"default_{templateSection.Key}") ? "expanded" : "collapsed")">
                            @foreach (var field in templateSection.Value)
                            {
                                <div class="soap-field">
                                    <div class="field-header">
                                        <label class="field-label">@field.Key</label>
                                        <Syncfusion.Blazor.Buttons.SfButton CssClass="edit-btn"
                                                                            IconCss="e-icons e-edit"
                                                                            OnClick="() => ToggleEdit(Guid.Empty, templateSection.Key, field.Key, true)">
                                        </Syncfusion.Blazor.Buttons.SfButton>
                                    </div>

                                    <div class="field-content">
                                        @{
                                            var fieldKey = $"default_{templateSection.Key}_{field.Key}";
                                            var isEditing = editingFields.Contains(fieldKey);
                                        }

                                        @if (isEditing)
                                        {
                                            <div class="edit-container">
                                                <Syncfusion.Blazor.Inputs.SfTextBox Value="@field.Value"
                                                                                    ValueChanged="@((string value) => UpdateDefaultFieldValue(templateSection.Key, field.Key, value))"
                                                                                    Multiline="true"
                                                                                    FloatLabelType="FloatLabelType.Never"
                                                                                    CssClass="edit-textbox"
                                                                                    Placeholder="@GetPlaceholderText(field.Key)">
                                                </Syncfusion.Blazor.Inputs.SfTextBox>
                                                <div class="edit-actions">
                                                    <Syncfusion.Blazor.Buttons.SfButton CssClass="save-field-btn"
                                                                                        OnClick="() => SaveDefaultField(templateSection.Key, field.Key)">
                                                        Save
                                                    </Syncfusion.Blazor.Buttons.SfButton>
                                                    <Syncfusion.Blazor.Buttons.SfButton CssClass="cancel-btn"
                                                                                        OnClick="() => CancelEdit(Guid.Empty, templateSection.Key, field.Key, true)">
                                                        Cancel
                                                    </Syncfusion.Blazor.Buttons.SfButton>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="field-display @(string.IsNullOrEmpty(field.Value) ? "empty" : "")">
                                                @if (string.IsNullOrEmpty(field.Value))
                                                {
                                                    <span class="placeholder-text">@GetPlaceholderText(field.Key)</span>
                                                }
                                                else
                                                {
                                                    <pre class="field-text">@field.Value</pre>
                                                }
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }

                <div class="template-actions">
                    <Syncfusion.Blazor.Buttons.SfButton CssClass="create-note-btn"
                                                        OnClick="CreateNewSoapNote">
                        <i class="e-icons e-plus"></i>
                        Create SOAP Note
                    </Syncfusion.Blazor.Buttons.SfButton>
                </div>
            </div>
        }
    </div>
</div>

<!-- Toast Notification -->
<Syncfusion.Blazor.Notifications.SfToast @ref="toastObj"
                                         Target=".soap-notes-container"
                                         Timeout="3000">
</Syncfusion.Blazor.Notifications.SfToast>

<style>
    .soap-notes-container {
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .soap-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        z-index: 100;
    }

    .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1200px;
        margin: 0 auto;
    }

    .back-section {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        transition: background-color 0.2s;
        min-width: 80px;
    }

        .back-section:hover {
            background: rgba(255,255,255,0.1);
        }

    .title-section {
        flex: 1;
        text-align: center;
    }

        .title-section h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

    .patient-info {
        font-size: 12px;
        opacity: 0.8;
    }

    .action-section {
        min-width: 100px;
        display: flex;
        justify-content: flex-end;
    }

    .save-btn {
        background: rgba(255,255,255,0.2) !important;
        border: 1px solid rgba(255,255,255,0.3) !important;
        color: white !important;
        border-radius: 6px !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
    }

        .save-btn:hover {
            background: rgba(255,255,255,0.3) !important;
        }

    .spinning {
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0%

    {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }

    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        background: white;
        margin: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    .soap-content {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        width: 100%;
    }

    .record-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .record-header {
        background: #f8f9fa;
        padding: 12px 16px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .record-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .record-date {
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .patient-name {
        font-size: 12px;
        color: #6c757d;
    }

    .record-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

        .record-status.editable {
            background: #d4edda;
            color: #155724;
        }

        .record-status.readonly {
            background: #f8d7da;
            color: #721c24;
        }

    .soap-section {
        border-bottom: 1px solid #e9ecef;
    }

        .soap-section:last-child {
            border-bottom: none;
        }

    .section-header {
        padding: 16px;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }

        .section-header:hover {
            background: #e9ecef;
        }

        .section-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #495057;
        }

    .expand-btn {
        background: transparent !important;
        border: none !important;
        color: #6c757d !important;
        min-width: 32px !important;
        height: 32px !important;
    }

    .section-content {
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

        .section-content.collapsed {
            max-height: 0;
        }

        .section-content.expanded {
            max-height: 1000px;
        }

    .soap-field {
        padding: 16px;
        border-bottom: 1px solid #f1f3f4;
    }

        .soap-field:last-child {
            border-bottom: none;
        }

    .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .field-label {
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    .edit-btn {
        background: transparent !important;
        border: 1px solid #dee2e6 !important;
        color: #6c757d !important;
        min-width: 32px !important;
        height: 32px !important;
        border-radius: 4px !important;
    }

        .edit-btn:hover {
            background: #f8f9fa !important;
            color: #495057 !important;
        }

    .field-content {
        min-height: 40px;
    }

    .field-display {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        min-height: 80px;
        cursor: text;
    }

        .field-display.empty {
            border-style: dashed;
            display: flex;
            align-items: center;
            justify-content: center;
        }

    .placeholder-text {
        color: #6c757d;
        font-style: italic;
        font-size: 14px;
    }

    .field-text {
        margin: 0;
        white-space: pre-wrap;
        font-family: inherit;
        color: #495057;
        line-height: 1.5;
    }

    .edit-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .edit-textbox {
        min-height: 100px !important;
    }

    .edit-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }

    .save-field-btn {
        background: #28a745 !important;
        border: none !important;
        color: white !important;
        padding: 6px 16px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
    }

    .cancel-btn {
        background: #6c757d !important;
        border: none !important;
        color: white !important;
        padding: 6px 16px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
    }

    .raw-notes-section {
        padding: 16px;
    }

    .raw-notes-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        margin-top: 8px;
    }

    .raw-notes-text {
        margin: 0;
        white-space: pre-wrap;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #495057;
        line-height: 1.4;
    }

    .default-soap-template {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .template-header {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        padding: 20px;
        text-align: center;
        border-bottom: 1px solid #e9ecef;
    }

        .template-header h4 {
            margin: 0 0 8px 0;
            color: #1976d2;
            font-size: 18px;
        }

    .template-subtitle {
        color: #666;
        font-size: 14px;
    }

    .template-actions {
        padding: 20px;
        text-align: center;
        background: #f8f9fa;
    }

    .create-note-btn {
        background: #667eea !important;
        border: none !important;
        color: white !important;
        padding: 12px 24px !important;
        border-radius: 6px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .soap-content

    {
        padding: 12px;
    }

    .header-content {
        flex-wrap: wrap;
        gap: 8px;
    }

    .title-section {
        order: -1;
        width: 100%;
        margin-bottom: 8px;
    }

    .field-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .edit-actions {
        flex-direction: column;
    }

    .section-header h4 {
        font-size: 14px;
    }

    .field-label {
        font-size: 13px;
    }

    .record-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    }
</style>

@code {
    [Parameter] public Guid PatientId { get; set; }
    [Parameter] public Guid? RecordingId { get; set; }

    private Syncfusion.Blazor.Inputs.SfTextBox? editTextBox;
    private Syncfusion.Blazor.Notifications.SfToast? toastObj;

    private List<SoapRecord> soapRecords = new();
    private Dictionary<string, Dictionary<string, string>> defaultSoapTemplate = new();
    private HashSet<string> expandedSections = new();
    private HashSet<string> editingFields = new();
    private Dictionary<string, string> fieldValues = new();

    private bool isLoading = false;
    private bool isSaving = false;
    private bool _disposed = false;
    private string currentPatientName = "";

    // Platform detection
    private bool IsWeb => OperatingSystem.IsBrowser();

    public class SoapRecord
    {
        public Guid Id { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public Guid PatientId { get; set; }
        public string? PatientName { get; set; }
        public DateTime DateTime { get; set; }
        public string? Notes { get; set; }
        public string? Transcription { get; set; }
        public bool? isEditable { get; set; }
        public bool Subscription { get; set; }
        public Dictionary<string, Dictionary<string, string>>? ParsedNotes { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        InitializeDefaultTemplate();
        await LoadSoapNotes();
    }

    private void InitializeDefaultTemplate()
    {
        defaultSoapTemplate = new Dictionary<string, Dictionary<string, string>>
        {
            ["Subjective"] = new Dictionary<string, string>
            {
                ["Chief Complaint"] = "",
                ["History of Present Illness"] = "",
                ["Review of Systems"] = "",
                ["Past Medical History"] = "",
                ["Medications"] = "",
                ["Allergies"] = "",
                ["Social History"] = ""
            },
            ["Objective"] = new Dictionary<string, string>
            {
                ["Vital Signs"] = "",
                ["Physical Examination"] = "",
                ["Laboratory Results"] = "",
                ["Imaging Results"] = "",
                ["Other Diagnostic Tests"] = ""
            },
            ["Assessment"] = new Dictionary<string, string>
            {
                ["Primary Diagnosis"] = "",
                ["Secondary Diagnoses"] = "",
                ["Differential Diagnosis"] = "",
                ["Clinical Impression"] = ""
            },
            ["Plan"] = new Dictionary<string, string>
            {
                ["Treatment Plan"] = "",
                ["Medications Prescribed"] = "",
                ["Follow-up Instructions"] = "",
                ["Patient Education"] = "",
                ["Referrals"] = "",
                ["Next Appointment"] = ""
            }
        };

        // Expand all sections by default
        foreach (var templateSection in defaultSoapTemplate.Keys)
        {
            expandedSections.Add($"default_{templateSection}");
        }
    }

    private async Task LoadSoapNotes()
    {
        if (_disposed) return;

        isLoading = true;
        StateHasChanged();

        try
        {
            List<Record> records;

            if (!IsWeb)
            {
                PatientId = new Guid("24E73BD4-1EA8-403C-98CD-DB2DFE6FA1D0");
                records = await NotesService.GetRecordsByPatientIdAsync(PatientId, Guid.Empty, false);
                
            }
            else
            {
                // For web, use sample data
                records = GetSampleRecords();
                
            }

            soapRecords = records
                .Where(r => r.isEditable == true && !string.IsNullOrEmpty(r.Notes))
                .Select(r => new SoapRecord
                {
                    Id = r.Id,
                    OrganizationId = r.OrganizationId,
                    PCPId = r.PCPId,
                    PatientId = r.PatientId,
                    PatientName = r.PatientName,
                    DateTime = r.DateTime,
                    Notes = r.Notes,
                    Transcription = r.Transcription,
                    isEditable = r.isEditable,
                    Subscription = r.Subscription,
                    ParsedNotes = ParseNotesJson(r.Notes)
                })
                .OrderByDescending(r => r.DateTime)
                .ToList();

            // Set current patient name
            if (soapRecords.Any() && !string.IsNullOrEmpty(soapRecords.First().PatientName))
            {
                currentPatientName = soapRecords.First().PatientName;
            }

            // Expand first section of each record by default
            foreach (var record in soapRecords)
            {
                if (record.ParsedNotes?.Any() == true)
                {
                    var firstSection = record.ParsedNotes.First().Key;
                    expandedSections.Add($"{record.Id}_{firstSection}");
                }
            }
        }
        catch (Exception ex)
        {
            await ShowToast($"Error loading SOAP notes: {ex.Message}", "error");
            Console.WriteLine($"Error loading SOAP notes: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<Record> GetSampleRecords()
    {
        var sampleNotes = JsonSerializer.Serialize(new Dictionary<string, Dictionary<string, string>>
        {
            ["Subjective"] = new Dictionary<string, string>
            {
                ["Chief Complaint"] = "Patient reports chest pain and shortness of breath for the past 2 days.",
                ["History of Present Illness"] = "65-year-old male with a history of hypertension presents with acute onset chest pain.",
                ["Review of Systems"] = "Positive for chest pain, shortness of breath. Negative for nausea, vomiting.",
                ["Past Medical History"] = "Hypertension, diabetes mellitus type 2, hyperlipidemia.",
                ["Medications"] = "Lisinopril 10mg daily, Metformin 500mg twice daily.",
                ["Allergies"] = "NKDA (No Known Drug Allergies)",
                ["Social History"] = "Former smoker, quit 5 years ago. Occasional alcohol use."
            },
            ["Objective"] = new Dictionary<string, string>
            {
                ["Vital Signs"] = "BP: 150/90, HR: 88, RR: 18, Temp: 98.6°F, O2 Sat: 96% on room air",
                ["Physical Examination"] = "Alert and oriented. Heart: Regular rate and rhythm, no murmurs. Lungs: Clear to auscultation bilaterally.",
                ["Laboratory Results"] = "Troponin I: 0.8 ng/mL (elevated), BNP: 450 pg/mL",
                ["Imaging Results"] = "Chest X-ray: No acute cardiopulmonary process. ECG: ST depression in leads II, III, aVF.",
                ["Other Diagnostic Tests"] = "Echocardiogram ordered."
            },
            ["Assessment"] = new Dictionary<string, string>
            {
                ["Primary Diagnosis"] = "Acute coronary syndrome, NSTEMI",
                ["Secondary Diagnoses"] = "Hypertension, uncontrolled. Diabetes mellitus type 2.",
                ["Differential Diagnosis"] = "Unstable angina, pulmonary embolism, aortic dissection.",
                ["Clinical Impression"] = "Patient presenting with acute coronary syndrome requiring immediate intervention."
            },
            ["Plan"] = new Dictionary<string, string>
            {
                ["Treatment Plan"] = "Admit to telemetry unit. Start dual antiplatelet therapy. Cardiology consultation.",
                ["Medications Prescribed"] = "Aspirin 325mg daily, Clopidogrel 75mg daily, Atorvastatin 80mg daily.",
                ["Follow-up Instructions"] = "Follow up with cardiology in 1 week. Return to ED if symptoms worsen.",
                ["Patient Education"] = "Discussed signs and symptoms of heart attack. Importance of medication compliance.",
                ["Referrals"] = "Cardiology consultation for cardiac catheterization evaluation.",
                ["Next Appointment"] = "Cardiology follow-up scheduled for next Tuesday at 2:00 PM."
            }
        });

        return new List<Record>
        {
            new Record
            {
                Id = Guid.NewGuid(),
                OrganizationId = Guid.Empty,
                PCPId = Guid.Empty,
                PatientId = PatientId,
                PatientName = "John Doe",
                DateTime = DateTime.Now.AddHours(-2),
                Notes = sampleNotes,
                Transcription = "Sample transcription text...",
                isEditable = true,
                Subscription = false
            }
        };
    }

    private Dictionary<string, Dictionary<string, string>>? ParseNotesJson(string? notesJson)
    {
        try
        {
            if (string.IsNullOrEmpty(notesJson))
                return null;

            return JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(notesJson);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error parsing notes JSON: {ex.Message}");
            return null;
        }
    }

    private void ToggleSection(Guid recordId, string sectionKey, bool isDefault = false)
    {
        var key = isDefault ? $"default_{sectionKey}" : $"{recordId}_{sectionKey}";

        if (expandedSections.Contains(key))
        {
            expandedSections.Remove(key);
        }
        else
        {
            expandedSections.Add(key);
        }
    }

    private void ToggleEdit(Guid recordId, string sectionKey, string fieldKey, bool isDefault = false)
    {
        var key = isDefault ? $"default_{sectionKey}_{fieldKey}" : $"{recordId}_{sectionKey}_{fieldKey}";

        if (editingFields.Contains(key))
        {
            editingFields.Remove(key);
        }
        else
        {
            editingFields.Add(key);
        }
    }

    private void UpdateFieldValue(Guid recordId, string sectionKey, string fieldKey, string value)
    {
        var record = soapRecords.FirstOrDefault(r => r.Id == recordId);
        if (record?.ParsedNotes != null && record.ParsedNotes.ContainsKey(sectionKey))
        {
            record.ParsedNotes[sectionKey][fieldKey] = value;
        }
    }

    private void UpdateDefaultFieldValue(string sectionKey, string fieldKey, string value)
    {
        if (defaultSoapTemplate.ContainsKey(sectionKey))
        {
            defaultSoapTemplate[sectionKey][fieldKey] = value;
        }
    }

    private async Task SaveField(Guid recordId, string sectionKey, string fieldKey)
    {
        try
        {
            var record = soapRecords.FirstOrDefault(r => r.Id == recordId);
            if (record?.ParsedNotes != null)
            {
                var updatedJson = JsonSerializer.Serialize(record.ParsedNotes);

                var updatedRecord = new Record
                {
                    Id = record.Id,
                    OrganizationId = record.OrganizationId,
                    PCPId = record.PCPId,
                    PatientId = record.PatientId,
                    PatientName = record.PatientName,
                    DateTime = record.DateTime,
                    Notes = updatedJson,
                    Transcription = record.Transcription,
                    isEditable = record.isEditable,
                    Subscription = record.Subscription
                };

                if (IsWeb)
                {
                    // For web, just show success message
                    await ShowToast("Field saved successfully!", "success");
                }
                else
                {
                    // For MAUI, use actual service
                    await NotesService.SaveRecordAsync(updatedRecord, record.OrganizationId, record.Subscription);
                    await ShowToast("Field saved successfully!", "success");
                }

                ToggleEdit(recordId, sectionKey, fieldKey);
            }
        }
        catch (Exception ex)
        {
            await ShowToast($"Error saving field: {ex.Message}", "error");
        }
    }

    private void SaveDefaultField(string sectionKey, string fieldKey)
    {
        ToggleEdit(Guid.Empty, sectionKey, fieldKey, true);
    }

    private void CancelEdit(Guid recordId, string sectionKey, string fieldKey, bool isDefault = false)
    {
        ToggleEdit(recordId, sectionKey, fieldKey, isDefault);
    }

    private async Task SaveAllNotes()
    {
        if (isSaving) return;

        isSaving = true;
        StateHasChanged();

        try
        {
            foreach (var record in soapRecords.Where(r => r.isEditable == true))
            {
                var updatedJson = JsonSerializer.Serialize(record.ParsedNotes);

                var updatedRecord = new Record
                {
                    Id = record.Id,
                    OrganizationId = record.OrganizationId,
                    PCPId = record.PCPId,
                    PatientId = record.PatientId,
                    PatientName = record.PatientName,
                    DateTime = record.DateTime,
                    Notes = updatedJson,
                    Transcription = record.Transcription,
                    isEditable = record.isEditable,
                    Subscription = record.Subscription
                };

                if (!IsWeb)
                {
                    await NotesService.SaveRecordAsync(updatedRecord, record.OrganizationId, record.Subscription);
                }
            }

            await ShowToast("All notes saved successfully!", "success");
        }
        catch (Exception ex)
        {
            await ShowToast($"Error saving notes: {ex.Message}", "error");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task CreateNewSoapNote()
    {
        try
        {
            var newNoteJson = JsonSerializer.Serialize(defaultSoapTemplate);

            var newRecord = new Record
            {
                Id = Guid.NewGuid(),
                OrganizationId = Guid.Empty,
                PCPId = Guid.Empty,
                PatientId = PatientId,
                PatientName = currentPatientName,
                DateTime = DateTime.Now,
                Notes = newNoteJson,
                Transcription = "",
                isEditable = true,
                Subscription = false
            };

            if (IsWeb)
            {
                // For web, add to local list
                soapRecords.Insert(0, new SoapRecord
                {
                    Id = newRecord.Id,
                    OrganizationId = newRecord.OrganizationId,
                    PCPId = newRecord.PCPId,
                    PatientId = newRecord.PatientId,
                    PatientName = newRecord.PatientName,
                    DateTime = newRecord.DateTime,
                    Notes = newRecord.Notes,
                    Transcription = newRecord.Transcription,
                    isEditable = newRecord.isEditable,
                    Subscription = newRecord.Subscription,
                    ParsedNotes = defaultSoapTemplate
                });
            }
            else
            {
                // For MAUI, save to service and reload
                await NotesService.SaveRecordAsync(newRecord, Guid.Empty, false);
                await LoadSoapNotes();
            }

            await ShowToast("New SOAP note created successfully!", "success");

            // Clear default template
            InitializeDefaultTemplate();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await ShowToast($"Error creating SOAP note: {ex.Message}", "error");
        }
    }

    private string GetPlaceholderText(string fieldKey)
    {
        return fieldKey switch
        {
            "Chief Complaint" => "Enter the patient's primary concern...",
            "History of Present Illness" => "Describe the current illness timeline...",
            "Vital Signs" => "Record temperature, blood pressure, heart rate...",
            "Physical Examination" => "Document physical findings...",
            "Primary Diagnosis" => "Enter the primary diagnosis...",
            "Treatment Plan" => "Outline the treatment approach...",
            _ => $"Enter {fieldKey.ToLower()}..."
        };
    }

    private async Task ShowToast(string message, string type)
    {
        if (toastObj != null)
        {
            var toastModel = new ToastModel
            {
                Title = type == "success" ? "Success" : "Error",
                Content = message,
                CssClass = type == "success" ? "e-toast-success" : "e-toast-danger",
                Icon = type == "success" ? "e-success toast-icons" : "e-error toast-icons"
            };

            await toastObj.ShowAsync(toastModel);
        }
    }

    private void NavigateBack()
    {
        if (IsWeb)
        {
            Navigation.NavigateTo($"/recorder/{PatientId}");
        }
        else
        {
            // For MAUI, navigate back
            Navigation.NavigateTo($"/recorder/{PatientId}");
        }
    }

    public void Dispose()
    {
        _disposed = true;
    }
}
