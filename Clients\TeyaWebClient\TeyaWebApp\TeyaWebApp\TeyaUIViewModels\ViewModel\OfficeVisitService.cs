﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public class OfficeVisitService : IOfficeVisitService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _Appointments;
        private readonly string _MemberService;
        private readonly ITokenService _tokenService;

        public OfficeVisitService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _Appointments = Environment.GetEnvironmentVariable("AppointmentsURL");
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Retrieves a list of office visit appointments by user ID.
        /// </summary>
        /// <param name="userid">User ID.</param>
        /// <returns>List of office visit appointments.</returns>
        public async Task<List<Appointment>> GetAppointmentsByuserIdAsync(Guid userid, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Appointments}/api/Appointments/user/{userid}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Appointment>>();
            }
            throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
        }

        /// <summary>
        /// Retrieves office visit members by a list of patient IDs.
        /// </summary>
        /// <param name="patientIds">List of patient IDs.</param>
        /// <returns>List of office visit members.</returns>
        public async Task<List<Office_visit_members>> GetMembersByUserIdsAsync(List<Guid> patientIds, Guid? orgId, bool subscription)
        {
            if (patientIds == null || !patientIds.Any())
                throw new ArgumentException(_localizer["InvalidPatientIds"]);

            var queryString = string.Join("&", patientIds.Select(id => $"patientIds={Uri.EscapeDataString(id.ToString())}"));
            var apiUrl = $"{_MemberService}/api/Registration/patientlistbyid/{orgId}/{subscription}?{queryString}";
            Console.WriteLine($"Calling API: {apiUrl}");

            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            Console.WriteLine($"Access Token: {accessToken}");
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
                return await response.Content.ReadFromJsonAsync<List<Office_visit_members>>();

            var errorContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Error Response: {response.StatusCode} - {errorContent}");
            throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
        }

        /// <summary>
        /// Retrieves a list of patient visits by user ID.
        /// </summary>
        /// <param name="userId">User ID.</param>
        /// <returns>List of office visit models.</returns>
        public async Task<List<OfficeVisitModel>> GetPatientListByIdAsync(Guid userId,Guid? OrgID,bool Subscription)
        {
            var appointments = await GetAppointmentsByuserIdAsync(userId, OrgID, Subscription);
            if(appointments.Count == 0)
            {
                return null;
            }
            var patientIds = appointments.Select(appointment => appointment.PatientId).Distinct().ToList();
            var members = await GetMembersByUserIdsAsync(patientIds, OrgID, Subscription);

            return appointments.Select(appointment =>
            {
                var member = members.FirstOrDefault(m => m.PatientId == appointment.PatientId);
                return new OfficeVisitModel
                {
                    Id = appointment.PatientId,
                    VisitType = appointment.VisitType,
                    AppointmentTime = appointment.StartTime,
                    PatientName = member?.UserName ?? "Unknown",
                    PR = appointment.Provider,
                    Reason = appointment.Reason,
                    Notes = appointment.Notes,
                    Sex = member?.SexualOrientation ?? "Unknown",
                    Dob = member?.DateOfBirth ?? default,
                    VisitStatus = appointment.VisitStatus,
                    ArrivalTime = appointment.StartTime,
                    Duration = appointment.StartTime.HasValue && appointment.EndTime.HasValue &&
           appointment.EndTime.Value != default(DateTime)
    ? $"{(appointment.EndTime.Value - appointment.StartTime.Value).TotalMinutes:F0} {_localizer["Minutes"]}"
    : _localizer["Unknown"],
                    RoomNumber = appointment.RoomNumber
                };
            }).ToList();
        }
    }
}
