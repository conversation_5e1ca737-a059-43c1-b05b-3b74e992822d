﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class OrderSetProcedures : IModel
    {
        public Guid Id { get; set; }
        public string CPTCode { get; set; }
        public string OrderedBy { get; set; }
        public string Description { get; set; }
        public string? Notes { get; set; }
        public DateTime OrderDate { get; set; }
        public string? AssessmentData { get; set; }
        public Guid? AssessmentId { get; set; }
        public Guid OrderSetId { get; set; }

    }
} 