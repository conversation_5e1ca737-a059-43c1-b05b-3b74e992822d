﻿using Microsoft.AspNetCore.Components;

namespace TeyaWebApp.Components.Templates
{
    public partial class InviteMail : ComponentBase
    {
        [Parameter]
        public string Email { get; set; }

        [Parameter]
        public string Password { get; set; }

        private string loginUrl;

        protected override void OnInitialized()
        {
            Email = _inviteMailParametersService.Email;
            Password = _inviteMailParametersService.Password;
            loginUrl = Environment.GetEnvironmentVariable("APP_LOGIN_URL"); 
        }
    }
}
