﻿using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Threading;
using System.Linq;
using static MudBlazor.Icons.Custom;
using Unity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Microsoft.Azure.Amqp.Framing;

namespace TeyaWebApp.Components.Pages
{
    public partial class SurgicalHistory
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public ISurgicalService _SurgicalService { get; set; }
        [Inject] private ILogger<SurgicalHistory> _logger { get; set; }
        [Inject] private IStringLocalizer<SurgicalHistory> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }

        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }

        [Inject] private ISnackbar Snackbar { get; set; }
        private MudDialog _surgicalhistory;
        private string _Surgery;

        private List<Surgical> surgicalhistory { get; set; }
        public enum Source { CMS,FDB }
        private string selectedDatabase = Source.CMS.ToString();
        public string ICDName { get; set; }

        private SfRichTextEditor RichTextEditor;
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        private List<FDB_ICD> fdb_ICD { get; set; } = new List<FDB_ICD>();


        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
        };

        public SfGrid<Surgical> SurgeryGrid { get; set; }
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientId { get; set; }
        private string editorContent;
        private Guid? OrgID { get; set; }
        private List<Surgical> deletesurgerylist { get; set; } = new List<Surgical>();
        private List<Surgical> AddList = new();

        /// <summary>
        /// Get All ICD Codes and Description from Database
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            editorContent = TotalText;
            OrgID = OrgId;
          
            fdb_ICD = await FDBService.GetICD();
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrgId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            try
            {
                _icdCodes = await _ICDService.GetAllICDCodesAsync();
              

                surgicalhistory = await _SurgicalService.GetSurgeryByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
              
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, Localizer["ErrorRetrievingICDCodes"]);
            }
        }

        /// <summary>
        /// To show the data in Rich Text Editor
        /// </summary>
        private void UpdateEditorContent()
        {
            editorContent = string.Join("<p>", surgicalhistory
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $"<strong>{s.CreatedDate.ToString("dd-MM-yyyy")}</strong> - {s.Surgery}"));
        }

        private void OnDatabaseChanged(string newDatabase)
        {
            selectedDatabase = newDatabase;
            ICDName = null; // Reset the drug name when database changes
            StateHasChanged(); // Ensure UI updates
        }

        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>
        private async Task OpenNewDialogBox()
        {
            await _surgicalhistory.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _surgicalhistory.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            IEnumerable<string> searchResults = Enumerable.Empty<string>();

            if (selectedDatabase == Source.CMS.ToString())
            {
                searchResults = _icdCodes
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (
                            (!string.IsNullOrEmpty(icd.Code) && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.Description) && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                    .ToList();
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                searchResults = fdb_ICD
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (icd.ICD_CD_TYPE == "06" || icd.ICD_CD_TYPE == "05") &&
                        (
                            (!string.IsNullOrEmpty(icd.ICD_CD) && icd.ICD_CD.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.ICD_DESC) && icd.ICD_DESC.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.ICD_CD} - {icd.ICD_DESC ?? ""}")
                    .ToList();
            }

            return Task.FromResult(searchResults);
        }

        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewSurgery()
        {
            if (DateTime.Now.Date < DateTime.Now.Date)
            {
                
                Snackbar.Add(Localizer["Future.DateError"], Severity.Warning);
                return;
            }

            var newSurgery = new Surgical
            {
                SurgeryId = Guid.NewGuid(),
                PatientId = PatientID,
                PCPId = Guid.Parse(User.id),
                OrganizationId = OrgId,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = PatientId,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Surgery = ICDName,
                IsActive = true,
            };

            AddList.Add(newSurgery);

            surgicalhistory.Add(newSurgery);
            await SurgeryGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private void ResetInputFields()
        {
            ICDName = string.Empty;
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<Surgical> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedSurgery = args.Data as Surgical;
                var existingItem = AddList.FirstOrDefault(v => v.SurgeryId == deletedSurgery.SurgeryId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deletesurgerylist.Add(args.Data);
                }
            }
        }

        /// <summary>
        /// To 
        /// </summary>
        /// <param name="args"></param>
        public async Task ActionBeginHandler(ActionEventArgs<Surgical> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {


                bool? result = await DialogService.ShowMessageBox(
                      Localizer["ConfirmDelete"],
                      Localizer["DeleteConfirmationMessage"],
                      yesText: Localizer["Yes"],
                      noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }

             
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.CreatedDate.Date > DateTime.Now.Date)
                {
                    Snackbar.Add(Localizer["Future.DateError"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _SurgicalService.AddSurgeryAsync(AddList, OrgID, Subscription);
            }
            await _SurgicalService.UpdateSurgeryListAsync(deletesurgerylist, OrgID, Subscription);
            await _SurgicalService.UpdateSurgeryListAsync(surgicalhistory, OrgID, Subscription);
            deletesurgerylist.Clear();
            AddList.Clear();
            //UpdateEditorContent();
            editorContent = GenerateRichTextContent(Data);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deletesurgerylist.Clear();
            AddList.Clear();
            surgicalhistory = await _SurgicalService.GetSurgeryByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }



        private string GenerateRichTextContent(string data)
        {
            data ??= string.Empty;

            string surgicalContent = (surgicalhistory != null && surgicalhistory.Any())
                ? string.Join(" ",
                    surgicalhistory.OrderByDescending(s => s.CreatedDate)
                        .Select(s => $"<ul><li style='margin-left: 20px;'><b>{s.CreatedDate:yyyy-MM-dd}</b> : {s.Surgery}</li></ul>"))
                : "<ul><li style='margin-left: 20px;'>No surgical history available.</li></ul>";

            return $@"<div>
        <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
        {data}
        <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
        {surgicalContent}
    </div>";
        }



        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(Data);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}