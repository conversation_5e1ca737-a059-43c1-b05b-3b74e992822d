<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="TeyaMobile.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:TeyaMobile"
    xmlns:strings="clr-namespace:TeyaMobile.TeyaMobileResource"
    Shell.FlyoutItemIsVisible="True"
    Shell.FlyoutBehavior="Flyout"
    FlyoutBackgroundColor="LightCyan"
    FlyoutWidth="250"
    Title="Teya AI Scribe">
    <!--xmlns:strings="clr-namespace:TeyaMobileHybrid.TeyaAiScribeMobileResource"-->
    

    <Shell.FlyoutHeader>
        <Label Text="Teya AI Scribe"
               FontSize="18"
               TextColor="Black"
               HorizontalTextAlignment="Center"
               Padding="10" />
    </Shell.FlyoutHeader>

    <!--<ShellContent
        Title="Main Page"
        ContentTemplate="{DataTemplate local:MainPage}"
        Route="MainPage" />-->

    <FlyoutItem Title="Home" Route="MainPage">
        <ShellContent ContentTemplate="{DataTemplate local:MainPage}" />
    </FlyoutItem>
    
   


    <ShellContent
         Title="Message"
         ContentTemplate="{DataTemplate local:Message}"
         Route="Message" />

    <ShellContent
        Title="Teya AI Scribe"
        ContentTemplate="{DataTemplate local:TeyaAI}"
        Route="TeyaAI" />
    <!--Title="{x:Static strings:TeyaAiScribeMobileRes.ChunksMerged}" way to use localizer, later implementation-->

    <ShellContent
        Title="Soap Notes"
        ContentTemplate="{DataTemplate local:TemplatesPage}"
        Route="TemplatesPage" />

    <ShellContent
        Title="Appointments"
        ContentTemplate="{DataTemplate local:AppointmentsDoctor}"
        Route="AppointmentsDoctor" />

    <ShellContent
        Title="PatientHomeView"
        ContentTemplate="{DataTemplate local:PatientHome}"
        Route="PatientHome" />

    <ShellContent
        Title="ProviderHomeView"
        ContentTemplate="{DataTemplate local:ProviderHome}"
        Route="ProviderHome" />
</Shell>
