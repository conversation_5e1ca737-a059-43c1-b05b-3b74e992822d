﻿using Markdig;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;

namespace TeyaWebApp.Components.Pages
{
    public partial class Encounters : ComponentBase
    {

        private SfGrid<GridRecord> Grid;
        private List<Record> records;
        private Guid? OrgID { get; set; }
        private Guid? PatientID { get; set; }
        private bool IsReadOnly { get; set; } = true;
        private List<GridRecord> gridData = new List<GridRecord>();
        private Record selectedRecord;


        [Inject] private ActiveUser User { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientID = _PatientService.PatientData.Id;
                OrgID = _PatientService.PatientData.OrganizationID;

                if (_PatientService.PatientData != null)
                {
                    var patientId = _PatientService.PatientData.Id;
                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(patientId,OrgID,false);
                }
                else
                {
                    records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id),OrgID,false);
                }

                if (records != null)
                {
                    gridData = records.Select(r => new GridRecord
                    {
                        Id = r.Id,
                        PatientName = r.PatientName,
                        DateTime = r.DateTime,
                        Physician = User.givenName,
                        ChiefComplaint = ExtractChiefComplaint(r.Notes),
                        Status = r.isEditable == false ? "Signed" : "Draft",
                        Billing = null,
                        OriginalRecord = r
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading records: {ex.Message}");
            }
        }


        private string patientFilter = string.Empty;
        private string providerFilter = string.Empty;

        private List<GridRecord> filteredGridData => gridData
            .Where(r => string.IsNullOrEmpty(patientFilter) ||
                       (r.PatientName?.Contains(patientFilter, StringComparison.OrdinalIgnoreCase) ?? false))
            .Where(r => string.IsNullOrEmpty(providerFilter) ||
                       (r.Physician?.Contains(providerFilter, StringComparison.OrdinalIgnoreCase) ?? false))
            .ToList();

        private void ResetFilters()
        {
            patientFilter = string.Empty;
            providerFilter = string.Empty;
        }
        //private void OnRowSelected(RowSelectEventArgs<GridRecord> args)
        //{
        //    selectedRecord = args.Data.OriginalRecord;
        //    StateHasChanged();
        //}

        private void ReturnToGrid()
        {
            selectedRecord = null;
            StateHasChanged();
        }

        /// <summary>
        /// Get the Cheif complain from the records
        /// </summary>
        /// <param name="notesJson"></param>
        /// <returns></returns>
      
        private string ExtractChiefComplaint(string notesJson)
        {
            string result = Localizer["NotAvailable"];

            try
            {
                if (string.IsNullOrEmpty(notesJson))
                    return result;

                var notes = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(notesJson);

                if (notes != null &&
                    notes.TryGetValue("Subjective", out var subjectiveSection) &&
                    subjectiveSection.TryGetValue("Chief Complaint", out var htmlContent))
                {
                    result = System.Text.RegularExpressions.Regex.Replace(htmlContent, "<.*?>", string.Empty);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting chief complaint: {ex.Message}");
            }

            return result;
        }

        private string GetAudioUrl(Guid id)
        {
            try
            {
                var baseUrl = Environment.GetEnvironmentVariable(Localizer["AudioUrl"]);
                return $"{baseUrl}/{id}.wav";
            }
            catch
            {
                return string.Empty;
            }
        }

        public class GridRecord
        {
            public Guid Id { get; set; }
            public string PatientName { get; set; }
            public DateTime DateTime { get; set; }
            public string Physician { get; set; }
            public string ChiefComplaint { get; set; }
            public string Billing { get; set; }
            public string Status { get; set; }
            public Record OriginalRecord { get; set; }
        }


        [Parameter] public EventCallback<Guid> OnRowClick { get; set; }

        private async Task OnRowSelected(RowSelectEventArgs<GridRecord> args)
        {
            Guid selectedId = args.Data.Id; // Assuming 'Id' is a Guid
            await OnRowClick.InvokeAsync(selectedId); // Send Guid to parent
        }




    }
}

