﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileModel.Model
{
    public class OfficeVisitModel : IModel
    {
        public Guid Id { get; set; }
        public string VisitType { get; set; }
        public DateTime AppointmentTime { get; set; }
        public string PatientName { get; set; }
        public string PR { get; set; }
        public string Reason { get; set; }
        public string Notes { get; set; }
        public string Sex { get; set; }
        public DateTime Dob { get; set; }
        public string VisitStatus { get; set; }
        public DateTime ArrivalTime { get; set; }
        public string Duration { get; set; }
        public string RoomNumber { get; set; }
    }
}
