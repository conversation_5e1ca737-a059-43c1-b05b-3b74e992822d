﻿@page "/counter"
@rendermode InteractiveServer
@using Microsoft.CognitiveServices.Speech;
@using Microsoft.CognitiveServices.Speech.Audio;
@using Microsoft.CognitiveServices.Speech.Translation;
@using Microsoft.SemanticKernel;
@using Microsoft.SemanticKernel.Connectors.OpenAI;
@using Microsoft.Extensions.Configuration;

<PageTitle>Speech to Text</PageTitle>

<h1>Speech to Text</h1>
<hr />
<p>
    <input type="text" class="form-control" @bind="InputValue" />
</p>
<button class="btn btn-primary" @onclick="Click_ReadMyVoice1">Read My Voice</button>

@code {
    private string? InputValue { get; set; }

    private async Task Click_ReadMyVoice1()
    {
        var speechConfig = SpeechConfig.FromSubscription("2066bb26ebda4009a647b5bbdaec9df3", "centralindia");
        speechConfig.SpeechRecognitionLanguage = "en-US";
        using var audioConfig = AudioConfig.FromDefaultMicrophoneInput();
        using var speechRecognizer = new SpeechRecognizer(speechConfig, audioConfig);
        var speechRecognitionResult = await speechRecognizer.RecognizeOnceAsync();
        await OutputSpeechRecognitionResult(speechRecognitionResult);
    }

    private async Task OutputSpeechRecognitionResult(SpeechRecognitionResult speechRecognitionResult)
    {
        switch (speechRecognitionResult.Reason)
        {
            case ResultReason.RecognizedSpeech:
                Kernel kernel = Kernel.CreateBuilder()
                    .AddAzureOpenAIChatCompletion("gpt-4o", "https://teyaopenai.openai.azure.com/", "bd759e57228940a39fb69f4a1981d2f7")
                    .Build();

                string prompt = $"Please summarize the following text in 20 words or less: {speechRecognitionResult.Text}";
                Console.WriteLine($"user >>> {prompt}");

                try
                {
                    string response = await kernel.InvokePromptAsync<string>(prompt,
                        new(new OpenAIPromptExecutionSettings() { MaxTokens = 400 }));
                    InputValue = response;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error invoking OpenAI: {ex.Message}");
                    InputValue = "Error processing speech. Please try again.";
                }
                break;

            case ResultReason.NoMatch:
                InputValue = "NOMATCH: Speech could not be recognized";
                break;

            case ResultReason.Canceled:
                var cancellation = CancellationDetails.FromResult(speechRecognitionResult);
                InputValue = $"CANCELED: Reason={cancellation.Reason}";
                if (cancellation.Reason == CancellationReason.Error)
                {
                    Console.WriteLine($"CANCELED: ErrorCode={cancellation.ErrorCode}");
                    Console.WriteLine($"CANCELED: ErrorDetails={cancellation.ErrorDetails}");
                    Console.WriteLine($"CANCELED: Double check the speech resource key and region.");
                }
                break;
        }

        StateHasChanged();
    }
}