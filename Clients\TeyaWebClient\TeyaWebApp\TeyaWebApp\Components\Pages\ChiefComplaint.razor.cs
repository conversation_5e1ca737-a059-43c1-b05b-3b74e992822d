﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using Blazored.SessionStorage;
using Microsoft.Extensions.Logging;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
namespace TeyaWebApp.Components.Pages
{
    public partial class ChiefComplaint:ComponentBase
    {
        [Inject] private ISessionStorageService SessionStorage { get; set; }
        [Inject] private SharedNotesService SharedNotesService { get; set; }

        private List<ChiefComplaintDTO> complaints = new();

        [Inject]
        private ILogger<ChiefComplaintDTO> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }

        public string Name { get; set; }
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        public List<string> DistinctComplaintSuggestions { get; private set; } = new();

        private List<ChiefComplaintDTO> addedComplaints = new();
        private List<ChiefComplaintDTO> updatedComplaints = new();
        private SfRichTextEditor richTextEditor;
        private MudDialog showBrowsePopup;
        private SfGrid<ChiefComplaintDTO> ComplaintsGrid;
        private string richTextContent = string.Empty;
        private string complaintDescription = string.Empty;
        private List<ChiefComplaintDTO> deleteList = new();
        private Guid? OrgID { get; set; }
        private bool isInternalUpdate { get; set; } = false;
        private Guid PatientId { get; set; }
        

        [Parameter]
        public Guid PatientID { get; set; }
        [Parameter]
        public Guid OrgId { get; set; }
        [Parameter]
        public string? Data { get; set; }

        [Parameter]
        public string? TotalText { get; set; }

        [Parameter]
        public EventCallback<string> OnValueChanged { get; set; }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = PatientID;
                OrgID = OrgId;
                richTextContent = TotalText;
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrgId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                await LoadComplaintsAsync();
                
                LocalData = await ChiefComplaintService.GetProcessedComplaintsAsync(OrgId,Subscription);
                await InvokeAsync(StateHasChanged);

            }
            catch (Exception ex)
            {
                Logger.LogError(ex,Localizer["InitializationError"]);
            }
        }
        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };

        private async Task AddComplaint()
        {
            if (string.IsNullOrWhiteSpace(complaintDescription))
            {
                Snackbar.Add(Localizer["EnterComplaintError"],Severity.Error);
                Logger.LogWarning(Localizer["EnterComplaintError"]);
                return;
            }

            // Check duplicates before adding
            var isDuplicate = complaints.Concat(addedComplaints)
                .Any(c => c.Description.Equals(complaintDescription, StringComparison.OrdinalIgnoreCase));

            if (isDuplicate)
            {
                Snackbar.Add(Localizer["Duplicate entry not allowed."], Severity.Error);
                return;
            }

            var newComplaint = new ChiefComplaintDTO
            {
                Id = Guid.NewGuid(),
                PatientId = PatientId,
                Description = complaintDescription,
                DateOfComplaint = DateTime.Now,
                OrganizationId = OrgId,
                PcpId = Guid.Parse(User.id),
                IsDeleted = false
            };

            try
            {
                complaints.Add(newComplaint);
                addedComplaints.Add(newComplaint);
                await ComplaintsGrid.Refresh();

                // Update LocalData only if not existing there
                if (!LocalData.Any(c => c.Description.Equals(complaintDescription, StringComparison.OrdinalIgnoreCase)))
                {
                    LocalData.Add(newComplaint);
                }

                complaintDescription = string.Empty;
                Logger.LogInformation(Localizer["ComplaintAdded"]);
            }
            
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error adding complaint"]);
            }
        }

        private string GenerateRichTextContent(string Data)
        {
            string complaintsContent =string.Join(" ",
            complaints.OrderByDescending(c => c.DateOfComplaint)
              .Select(c => $"<ul><li style='margin-left: 20px;'><b>{c.DateOfComplaint:yyyy-MM-dd}</b> : {c.Description}</li></ul>"));
            return $@"<div>
            <h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
            {Data}
            <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
            {complaintsContent}
            </div>";
        }


        private async Task ActionBeginHandler(ActionEventArgs<ChiefComplaintDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    Localizer["Confirm Delete"],
                    Localizer["Do you want to delete this entry?"],
                    yesText: Localizer["Yes"],
                    noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return; 
                }

                // Only mark for deletion if user confirms
                args.Data.IsDeleted = true;
                deleteList.Add(args.Data);
                Snackbar.Add(Localizer["Entry deleted successfully."], Severity.Warning);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Check duplicates in both existing data and pending additions
                bool isDuplicate = complaints.Concat(addedComplaints)
                    .Where(c => !deleteList.Contains(c)) // Exclude deleted items
                    .Any(c =>
                        c.Description?.Trim().Equals(args.Data.Description?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                        c.Id != args.Data.Id);

                if (isDuplicate)
                {
                    Snackbar.Add(Localizer["Duplicate entry not allowed."], Severity.Error);
                    args.Cancel = true;
                    await ComplaintsGrid.CloseEditAsync();
                    CloseBrowsePopup();
                    return;
                }

                if (!addedComplaints.Contains(args.Data) && !updatedComplaints.Contains(args.Data))
                {
                    updatedComplaints.Add(args.Data);
                }
            }
        }
        private async Task HandleRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            richTextContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            richTextContent = GenerateRichTextContent(Data);
            await InvokeAsync(StateHasChanged);
           
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(richTextContent);
            }
           
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task SaveChanges()
        {
            try
            {
                if (addedComplaints.Any())
                {
                    foreach (var newEntry in addedComplaints)
                    {
                        await ChiefComplaintService.AddAsync(newEntry, OrgID, Subscription);
                    }
                    List<ChiefComplaintDTO> CheifComplaintsById = (await ChiefComplaintService.GetByPatientIdAsync(PatientId, OrgID, Subscription)).ToList();
                    var filteredComplaints = CheifComplaintsById
                        .GroupBy(c => c.Description)
                        .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First()) 
                        .ToList();
                    SharedNotesService.AddChiefComplaints(addedComplaints);
                    addedComplaints.Clear();
                }
                if (updatedComplaints.Any())
                {
                    await ChiefComplaintService.UpdateComplaintListAsync(updatedComplaints, OrgID, Subscription);
                    updatedComplaints.Clear();
                }

                if (deleteList.Any())
                {
                    await ChiefComplaintService.UpdateComplaintListAsync(deleteList, OrgID, Subscription);
                    deleteList.Clear();
                }

                await LoadComplaintsAsync();

                richTextContent = GenerateRichTextContent(Data);
                await HandleDynamicComponentUpdate();
                CloseBrowsePopup();
                await richTextEditor.RefreshUIAsync();
                
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                CloseBrowsePopup();
                Logger.LogInformation(Localizer["RecordSaved"]);
            }
            
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["Error saving changes"]);

            }
        }

        private async Task CancelChanges()
        {
            deleteList.Clear();
            addedComplaints.Clear();
            updatedComplaints.Clear();
            await LoadComplaintsAsync();
            CloseBrowsePopup();
            Snackbar.Add(Localizer["ChangesCanceled"], Severity.Info);
            Logger.LogInformation(Localizer["ChangesCanceled"]);
        }

        private async Task LoadComplaintsAsync()
        {
            try
            {

                
                complaints = (await ChiefComplaintService.LoadComplaintsAsync(PatientId, OrgID, Subscription))
                            .OrderByDescending(c => c.DateOfComplaint)
                            .ToList();
               
                richTextContent = GenerateRichTextContent(Data);
                 StateHasChanged();
                if (ComplaintsGrid != null)
                    await ComplaintsGrid.Refresh();
            }
            catch (Exception ex)
            {

                Logger.LogError(ex,Localizer["ErrorLoadingComplaints"]);

            }
        }

        private void CloseBrowsePopup()
        {
            complaintDescription = string.Empty;
            showBrowsePopup?.CloseAsync();
        }

        private async Task OpenBrowsePopupAsync()
        {
            showBrowsePopup.ShowAsync();
            if (ComplaintsGrid != null)
                await ComplaintsGrid.Refresh();

        }
    }
}
