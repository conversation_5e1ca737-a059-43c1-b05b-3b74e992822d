﻿using Syncfusion.Blazor.Schedule;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Shared.Pages
{
    public partial class Appointments
    {
        private Syncfusion.Blazor.Schedule.SfSchedule<AppointmentData>? ScheduleRef;
        private Syncfusion.Blazor.Popups.SfDialog? DatePickerDialog;
        private CancellationTokenSource? _cancellationTokenSource;

        private DateTime selectedDate = DateTime.Today;
        private bool isDatePickerVisible = false;
        private bool isLoading = false;
        private string errorMessage = "";
        private string schedulerHeight = "calc(100vh - 80px)";

        private List<AppointmentData> appointments = new();
        private int totalAppointments = 0;
        private int completedAppointments = 0;
        private int pendingAppointments = 0;

        private bool _disposed = false;

        // Platform detection
        private bool IsWeb => OperatingSystem.IsBrowser();
        private bool IsAndroid => OperatingSystem.IsAndroid();
        private bool IsIOS => OperatingSystem.IsIOS();

        protected override async Task OnInitializedAsync()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            await LoadAppointments();
        }

        private async Task LoadAppointments()
        {
            if (_disposed) return;

            isLoading = true;
            errorMessage = "";
            StateHasChanged();

            try
            {
                List<Appointment> appointmentList;

                if (!IsWeb)
                {
                    // For MAUI platforms, use your actual service
                    appointmentList = await AppointmentService.GetAllAppointmentsAsync();

                    // Alternative: Use GetAppointmentsAsync for specific date (commented out for now)
                    // var orgId = Guid.Parse("your-org-id"); // Replace with actual org ID
                    // var subscription = true; // Replace with actual subscription status
                    // appointmentList = await AppointmentService.GetAppointmentsAsync(selectedDate, orgId, subscription);
                    // For web, use sample data to avoid platform-specific errors
                    appointmentList = GetSampleAppointments();
                    Console.WriteLine("Using sample data for web platform");
                }
                else
                {
                    // For web, use sample data to avoid platform-specific errors
                    appointmentList = GetSampleAppointments();
                    Console.WriteLine("Using sample data for web platform");
                }

                if (_disposed) return;

                appointments = appointmentList
                    .Where(a => a.AppointmentDate.Date == selectedDate.Date)
                    .Select(a => new AppointmentData
                    {
                        Id = a.Id,
                        Subject = a.PatientName ?? "Unknown Patient",
                        PatientName = a.PatientName ?? "Unknown Patient",
                        StartTime = a.StartTime ?? a.AppointmentDate,
                        EndTime = a.EndTime ?? a.AppointmentDate.AddMinutes(30),
                        Provider = a.Provider ?? "Unknown Provider",
                        VisitType = a.VisitType ?? "Consultation",
                        VisitStatus = a.VisitStatus ?? "Scheduled",
                        Reason = a.Reason ?? "",
                        Notes = a.Notes ?? "",
                        RoomNumber = a.RoomNumber ?? "",
                        PatientId = a.PatientId
                    }).ToList();

                UpdateSummary();
            }
            catch (HttpRequestException httpEx)
            {
                errorMessage = $"Network error: {httpEx.Message}";
                Console.WriteLine($"HTTP error loading appointments: {httpEx.Message}");

                // Fallback to sample data on network error
                if (IsWeb)
                {
                    await LoadSampleData();
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error loading appointments: {ex.Message}";
                Console.WriteLine($"Error loading appointments: {ex.Message}");

                // Fallback to sample data on any error
                await LoadSampleData();
            }
            finally
            {
                isLoading = false;
                if (!_disposed)
                {
                    StateHasChanged();
                }
            }
        }

        private List<Appointment> GetSampleAppointments()
        {
            return new List<Appointment>
        {
            new Appointment
            {
                Id = Guid.NewGuid(),
                PatientName = "John Doe",
                StartTime = DateTime.Today.AddHours(9),
                EndTime = DateTime.Today.AddHours(9).AddMinutes(30),
                AppointmentDate = DateTime.Today,
                VisitType = "Consultation",
                VisitStatus = "Scheduled",
                Reason = "Regular Checkup",
                RoomNumber = "101",
                PatientId = Guid.NewGuid(),
                Provider = "Dr. Smith",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            },
            new Appointment
            {
                Id = Guid.NewGuid(),
                PatientName = "Jane Smith",
                StartTime = DateTime.Today.AddHours(11),
                EndTime = DateTime.Today.AddHours(11).AddMinutes(30),
                AppointmentDate = DateTime.Today,
                VisitType = "Follow-up",
                VisitStatus = "Confirmed",
                Reason = "Blood Test Results",
                RoomNumber = "102",
                PatientId = Guid.NewGuid(),
                Provider = "Dr. Johnson",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            },
            new Appointment
            {
                Id = Guid.NewGuid(),
                PatientName = "Bob Johnson",
                StartTime = DateTime.Today.AddHours(14),
                EndTime = DateTime.Today.AddHours(14).AddMinutes(30),
                AppointmentDate = DateTime.Today,
                VisitType = "Emergency",
                VisitStatus = "Pending",
                Reason = "Chest Pain",
                RoomNumber = "ER-1",
                PatientId = Guid.NewGuid(),
                Provider = "Dr. Williams",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            },
            new Appointment
            {
                Id = Guid.NewGuid(),
                PatientName = "Alice Brown",
                StartTime = DateTime.Today.AddHours(16),
                EndTime = DateTime.Today.AddHours(16).AddMinutes(30),
                AppointmentDate = DateTime.Today,
                VisitType = "Consultation",
                VisitStatus = "Completed",
                Reason = "Diabetes Follow-up",
                RoomNumber = "103",
                PatientId = Guid.NewGuid(),
                Provider = "Dr. Davis",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            }
        };
        }

        private async Task LoadSampleData()
        {
            var sampleAppointments = GetSampleAppointments();

            appointments = sampleAppointments
                .Where(a => a.AppointmentDate.Date == selectedDate.Date)
                .Select(a => new AppointmentData
                {
                    Id = a.Id,
                    Subject = a.PatientName ?? "Unknown Patient",
                    PatientName = a.PatientName ?? "Unknown Patient",
                    StartTime = a.StartTime ?? a.AppointmentDate,
                    EndTime = a.EndTime ?? a.AppointmentDate.AddMinutes(30),
                    Provider = a.Provider ?? "Dr. Smith",
                    VisitType = a.VisitType ?? "Consultation",
                    VisitStatus = a.VisitStatus ?? "Scheduled",
                    Reason = a.Reason ?? "",
                    Notes = a.Notes ?? "",
                    RoomNumber = a.RoomNumber ?? "",
                    PatientId = a.PatientId
                }).ToList();

            UpdateSummary();
            errorMessage = ""; // Clear error message when using sample data
        }

        private void UpdateSummary()
        {
            totalAppointments = appointments.Count;
            completedAppointments = appointments.Count(a => a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase));
            pendingAppointments = appointments.Count(a => !a.VisitStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase) &&
                                                         !a.VisitStatus.Equals("Cancelled", StringComparison.OrdinalIgnoreCase));
        }

        private string GetAppointmentStatusClass(AppointmentData appointment)
        {
            return appointment.VisitStatus.ToLower().Replace(" ", "-") switch
            {
                "completed" => "completed",
                "cancelled" => "cancelled",
                "confirmed" => "confirmed",
                "pending" => "pending",
                _ => "scheduled"
            };
        }

        private void OpenDatePicker()
        {
            if (_disposed) return;
            isDatePickerVisible = true;
        }

        private async Task OnDateSelected(DateTime date)
        {
            if (_disposed) return;
            selectedDate = date;
            isDatePickerVisible = false;
            await LoadAppointments();
        }

        private void OnDatePickerVisibilityChanged(bool visible)
        {
            if (_disposed) return;
            isDatePickerVisible = visible;
        }

        private async Task RetryLoadAppointments()
        {
            if (_disposed) return;
            await LoadAppointments();
        }

        private async Task OnAppointmentClick(EventClickArgs<AppointmentData> args)
        {
            if (_disposed || args.Event == null) return;
            await OnAppointmentCardClick(args.Event);
        }

        private async Task OnAppointmentCardClick(AppointmentData appointment)
        {
            if (_disposed || appointment == null) return;

            try
            {
                Navigation.NavigateTo($"/recorder/{appointment.PatientId}?appointmentId={appointment.Id}");
            }
            catch (Exception ex)
            {
                if (!_disposed)
                {
                    Console.WriteLine($"Error handling appointment click: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            _disposed = true;
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
        }
    }
}
