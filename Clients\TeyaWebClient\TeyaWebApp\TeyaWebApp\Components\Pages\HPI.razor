﻿@page "/HPI"

@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns



<!-- The Rich-Text Box for Display -->

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))" >

    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" Size="Size.Small" @onclick="OpenHpiDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_hpiDialog" Style="width: 85vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["HistoryOfPresentIllness"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <!-- Combined row for search and button -->
                    <MudItem xs="12" Style="display: flex; gap: 10px; align-items: center;">
                       
                        <div style="flex: 1; max-width: 35%;">
                            <SfAutoComplete TValue="string"
                                            TItem="Symptoms"
                                            Placeholder="Search Symptom"
                                            @bind-Value="Symptom"
                                            DataSource="@symptomSuggestions"
                                            Style="height: 40px; width: 100%;"
                                            CssClass="custom-autocomplete">
                                <AutoCompleteFieldSettings Value="Symptom" Text="Symptom"></AutoCompleteFieldSettings>
                            </SfAutoComplete>
                        </div>

                      
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewEntry"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 100px; height: 40px; margin-left: 10px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <!-- HPI Table -->
                <SfGrid @ref="HpiGrid"
                        TValue="HistoryOfPresentIllness"
                        Style="font-size: 0.85rem; margin-top: 24px;"
                        DataSource="@hpiEntries"
                        AllowPaging="true"
                        GridLines="GridLine.Both"
                        PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="HistoryOfPresentIllness"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="Symptoms" HeaderText="@Localizer["Symptom"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                        <GridColumn Field="Location" HeaderText="@Localizer["Location"]" TextAlign="TextAlign.Center" Width="150"></GridColumn>
                        <GridColumn Field="Severity" HeaderText="@Localizer["Severity"]" TextAlign="TextAlign.Center" Width="100"></GridColumn>
                        <GridColumn Field="StartDate" HeaderText="@Localizer["StartDate"]" TextAlign="TextAlign.Center" Width="120" Format="MM/dd/y"></GridColumn>
                        <GridColumn Field="EndDate" HeaderText="@Localizer["EndDate"]" TextAlign="TextAlign.Center" Width="120" Format="MM/dd/y"></GridColumn>
                        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" Width="150"></GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>