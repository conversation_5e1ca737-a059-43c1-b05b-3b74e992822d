﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaMobileViewModel.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public class GraphAdminService:IGraphAdminService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<GraphAdminService> _logger;
        private readonly string _tenantId;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private string _adminAccessToken;
        private DateTime _tokenExpiration = DateTime.MinValue;

        public GraphAdminService(HttpClient httpClient, ILogger<GraphAdminService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _tenantId = Environment.GetEnvironmentVariable("ServicePrincipleTenantId");
            _clientId = Environment.GetEnvironmentVariable("ServicePrincipleClientId");
            _clientSecret = Environment.GetEnvironmentVariable("ServicePrincipleSecret");
        }

        public async Task<string> GetAdminAccessTokenAsync()
        {
            if (_adminAccessToken != null && DateTime.UtcNow < _tokenExpiration.AddMinutes(-5))
            {
                return _adminAccessToken;
            }

            var tokenUrl = $"https://login.microsoftonline.com/{_tenantId}/oauth2/v2.0/token";
            var content = new FormUrlEncodedContent(new[]
            {
            new KeyValuePair<string, string>("grant_type", "client_credentials"),
            new KeyValuePair<string, string>("client_id", _clientId),
            new KeyValuePair<string, string>("client_secret", _clientSecret),
            new KeyValuePair<string, string>("scope", "https://graph.microsoft.com/.default")
        });

            var response = await _httpClient.PostAsync(tokenUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Failed to get admin token: {response.StatusCode}");
            }

            var tokenResponse = await JsonSerializer.DeserializeAsync<JsonElement>(await response.Content.ReadAsStreamAsync());
            _adminAccessToken = tokenResponse.GetProperty("access_token").GetString();
            _tokenExpiration = DateTime.UtcNow.AddSeconds(tokenResponse.GetProperty("expires_in").GetInt32());

            return _adminAccessToken;
        }

        
    }
}
