﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileModel.Model
{
    public class ChiefComplaintDTO
{
    public Guid Id { get; set; }
    public Guid PatientId { get; set; }  
    public string Description { get; set; }
    public DateTime DateOfComplaint { get; set; }
    public Guid OrganizationId { get; set; }  
    public Guid? PcpId { get; set; }  
    public bool IsDeleted { get; set; } = false;  
    public object ComplaintDate { get; set; } 
}

}
