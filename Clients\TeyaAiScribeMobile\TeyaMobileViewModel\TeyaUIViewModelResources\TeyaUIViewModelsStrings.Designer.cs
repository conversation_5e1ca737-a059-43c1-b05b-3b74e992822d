﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TeyaMobileViewModel.TeyaUIViewModelResources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class TeyaUIViewModelsStrings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TeyaUIViewModelsStrings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TeyaMobileViewModel.TeyaUIViewModelResources.TeyaUIViewModelsStrings", typeof(TeyaUIViewModelsStrings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 0.
        /// </summary>
        public static string _0_ {
            get {
                return ResourceManager.GetString("\"0\"", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1024.
        /// </summary>
        public static string _1024_ {
            get {
                return ResourceManager.GetString("\"1024\"", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 500.
        /// </summary>
        public static string _500_ {
            get {
                return ResourceManager.GetString("\"500\"", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {productId}.
        /// </summary>
        public static string _productId_ {
            get {
                return ResourceManager.GetString("{productId}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to true.
        /// </summary>
        public static string _true {
            get {
                return ResourceManager.GetString("true", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activated.
        /// </summary>
        public static string Activated {
            get {
                return ResourceManager.GetString("Activated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Appointment.
        /// </summary>
        public static string AddAppointment {
            get {
                return ResourceManager.GetString("AddAppointment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  API Response: {ResponseData}.
        /// </summary>
        public static string API_Response___ResponseData_ {
            get {
                return ResourceManager.GetString("API Response: {ResponseData}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:MembersUrl.
        /// </summary>
        public static string ApiSettings_MembersUrl {
            get {
                return ResourceManager.GetString("ApiSettings:MembersUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:ProductsUrl.
        /// </summary>
        public static string ApiSettings_ProductsUrl {
            get {
                return ResourceManager.GetString("ApiSettings:ProductsUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:RegistrationUrl.
        /// </summary>
        public static string ApiSettings_RegistrationUrl {
            get {
                return ResourceManager.GetString("ApiSettings:RegistrationUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:UpdateAccessUrl.
        /// </summary>
        public static string ApiSettings_UpdateAccessUrl {
            get {
                return ResourceManager.GetString("ApiSettings:UpdateAccessUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to appointments.
        /// </summary>
        public static string appointments {
            get {
                return ResourceManager.GetString("appointments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessment.
        /// </summary>
        public static string Assessment {
            get {
                return ResourceManager.GetString("Assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to audio/wav.
        /// </summary>
        public static string audio_wav {
            get {
                return ResourceManager.GetString("audio/wav", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BlazorAudioRecorder.Initialize.
        /// </summary>
        public static string BlazorAudioRecorder_Initialize {
            get {
                return ResourceManager.GetString("BlazorAudioRecorder.Initialize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BlazorAudioRecorder.PauseRecord.
        /// </summary>
        public static string BlazorAudioRecorder_PauseRecord {
            get {
                return ResourceManager.GetString("BlazorAudioRecorder.PauseRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BlazorAudioRecorder.ResumeRecord.
        /// </summary>
        public static string BlazorAudioRecorder_ResumeRecord {
            get {
                return ResourceManager.GetString("BlazorAudioRecorder.ResumeRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BlazorAudioRecorder.StartRecord.
        /// </summary>
        public static string BlazorAudioRecorder_StartRecord {
            get {
                return ResourceManager.GetString("BlazorAudioRecorder.StartRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BlazorAudioRecorder.StopRecord.
        /// </summary>
        public static string BlazorAudioRecorder_StopRecord {
            get {
                return ResourceManager.GetString("BlazorAudioRecorder.StopRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Byproduct.
        /// </summary>
        public static string Byproduct {
            get {
                return ResourceManager.GetString("Byproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cache hit for key: {Key}.
        /// </summary>
        public static string CacheHit {
            get {
                return ResourceManager.GetString("CacheHit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to users:all.
        /// </summary>
        public static string cacheKey {
            get {
                return ResourceManager.GetString("cacheKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cache miss for key: {Key}.
        /// </summary>
        public static string CacheMiss {
            get {
                return ResourceManager.GetString("CacheMiss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Medication.
        /// </summary>
        public static string CurrentMedication {
            get {
                return ResourceManager.GetString("CurrentMedication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date:.
        /// </summary>
        public static string Date_ {
            get {
                return ResourceManager.GetString("Date:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Of Birth.
        /// </summary>
        public static string DateOfBirth {
            get {
                return ResourceManager.GetString("DateOfBirth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EmailRequired.
        /// </summary>
        public static string EmailRequired {
            get {
                return ResourceManager.GetString("EmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to en-US.
        /// </summary>
        public static string en_US {
            get {
                return ResourceManager.GetString("en-US", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Time.
        /// </summary>
        public static string EndTime {
            get {
                return ResourceManager.GetString("EndTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Time:.
        /// </summary>
        public static string EndTime_ {
            get {
                return ResourceManager.GetString("EndTime:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter search term.
        /// </summary>
        public static string EnterSearchTerm {
            get {
                return ResourceManager.GetString("EnterSearchTerm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error deserializing response: {0}.
        /// </summary>
        public static string Error_deserializing_response___0_ {
            get {
                return ResourceManager.GetString("Error deserializing response: {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving Licenses.
        /// </summary>
        public static string Error_Saving_Licenses {
            get {
                return ResourceManager.GetString("Error Saving Licenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error accessing cache for key: {Key}.
        /// </summary>
        public static string ErrorCache {
            get {
                return ResourceManager.GetString("ErrorCache", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error During Registration.
        /// </summary>
        public static string ErrorDuringRegistration {
            get {
                return ResourceManager.GetString("ErrorDuringRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error processing complaints.
        /// </summary>
        public static string ErrorFetchingComplaints {
            get {
                return ResourceManager.GetString("ErrorFetchingComplaints", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Fetching Licenses.
        /// </summary>
        public static string ErrorFetchingLicenses {
            get {
                return ResourceManager.GetString("ErrorFetchingLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Fetching Members For Product.
        /// </summary>
        public static string ErrorFetchingMembersForProduct {
            get {
                return ResourceManager.GetString("ErrorFetchingMembersForProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingProducts.
        /// </summary>
        public static string ErrorFetchingProducts {
            get {
                return ResourceManager.GetString("ErrorFetchingProducts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Loading Records.
        /// </summary>
        public static string ErrorLoadingRecords {
            get {
                return ResourceManager.GetString("ErrorLoadingRecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorPostingData:{StatusCode}.
        /// </summary>
        public static string ErrorPostingData__StatusCode_ {
            get {
                return ResourceManager.GetString("ErrorPostingData:{StatusCode}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error removing cache key: {Key}.
        /// </summary>
        public static string ErrorRemovingCache {
            get {
                return ResourceManager.GetString("ErrorRemovingCache", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License Retrieval Failure.
        /// </summary>
        public static string ErrorSavingLicenses {
            get {
                return ResourceManager.GetString("ErrorSavingLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSavingMemberAccessUpdates.
        /// </summary>
        public static string ErrorSavingMemberAccessUpdates {
            get {
                return ResourceManager.GetString("ErrorSavingMemberAccessUpdates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving Record.
        /// </summary>
        public static string ErrorSavingRecord {
            get {
                return ResourceManager.GetString("ErrorSavingRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Family History.
        /// </summary>
        public static string FamilyHistory {
            get {
                return ResourceManager.GetString("FamilyHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Has Access.
        /// </summary>
        public static string Has_Access {
            get {
                return ResourceManager.GetString("Has Access", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hospitalization History.
        /// </summary>
        public static string HospitalizationHistory {
            get {
                return ResourceManager.GetString("HospitalizationHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Id.
        /// </summary>
        public static string Id {
            get {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidEmailFormat.
        /// </summary>
        public static string InvalidEmailFormat {
            get {
                return ResourceManager.GetString("InvalidEmailFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Json Deserialization Error.
        /// </summary>
        public static string JsonDeserializationError {
            get {
                return ResourceManager.GetString("JsonDeserializationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License Retrieval Failure.
        /// </summary>
        public static string LicenseRetrievalFailure {
            get {
                return ResourceManager.GetString("LicenseRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medical History.
        /// </summary>
        public static string MedicalHistory {
            get {
                return ResourceManager.GetString("MedicalHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Member Registration Error.
        /// </summary>
        public static string MemberRegistrationError {
            get {
                return ResourceManager.GetString("MemberRegistrationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mic.
        /// </summary>
        public static string mic {
            get {
                return ResourceManager.GetString("mic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Records Found.
        /// </summary>
        public static string NoRecordsFound {
            get {
                return ResourceManager.GetString("NoRecordsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No speech could be recognized.
        /// </summary>
        public static string NoSpeechRecognized {
            get {
                return ResourceManager.GetString("NoSpeechRecognized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No user found for selecting criteria.
        /// </summary>
        public static string NoUserFound {
            get {
                return ResourceManager.GetString("NoUserFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PasswordRequired.
        /// </summary>
        public static string PasswordRequired {
            get {
                return ResourceManager.GetString("PasswordRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords Do Not Match.
        /// </summary>
        public static string PasswordsDoNotMatch {
            get {
                return ResourceManager.GetString("PasswordsDoNotMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient:.
        /// </summary>
        public static string Patient_ {
            get {
                return ResourceManager.GetString("Patient:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Name.
        /// </summary>
        public static string PatientName {
            get {
                return ResourceManager.GetString("PatientName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to pause.
        /// </summary>
        public static string pause {
            get {
                return ResourceManager.GetString("pause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone Number.
        /// </summary>
        public static string PhoneNumber {
            get {
                return ResourceManager.GetString("PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Play.
        /// </summary>
        public static string Play {
            get {
                return ResourceManager.GetString("Play", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to play_arrow.
        /// </summary>
        public static string play_arrow {
            get {
                return ResourceManager.GetString("play_arrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to product Id.
        /// </summary>
        public static string productId {
            get {
                return ResourceManager.GetString("productId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Retrieval Failure.
        /// </summary>
        public static string ProductRetrievalFailure {
            get {
                return ResourceManager.GetString("ProductRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RecognitionaCanceled:{ErrorDetails}.
        /// </summary>
        public static string RecognitionaCanceled__ErrorDetails_ {
            get {
                return ResourceManager.GetString("RecognitionaCanceled:{ErrorDetails}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recognized:{Text}.
        /// </summary>
        public static string Recognized__Text_ {
            get {
                return ResourceManager.GetString("Recognized:{Text}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record Date:.
        /// </summary>
        public static string RecordDate_ {
            get {
                return ResourceManager.GetString("RecordDate:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record ID:.
        /// </summary>
        public static string RecordID_ {
            get {
                return ResourceManager.GetString("RecordID:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reenter Password Required.
        /// </summary>
        public static string ReenterPasswordRequired {
            get {
                return ResourceManager.GetString("ReenterPasswordRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration Failed.
        /// </summary>
        public static string RegistrationFailed {
            get {
                return ResourceManager.GetString("RegistrationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration Successful.
        /// </summary>
        public static string RegistrationSuccessful {
            get {
                return ResourceManager.GetString("RegistrationSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Removed {Count} cache keys matching pattern: {Pattern}.
        /// </summary>
        public static string RemovedCache {
            get {
                return ResourceManager.GetString("RemovedCache", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error removing cache keys with pattern: {Pattern}.
        /// </summary>
        public static string RemoveError {
            get {
                return ResourceManager.GetString("RemoveError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request successful.
        /// </summary>
        public static string Requestsuccessful {
            get {
                return ResourceManager.GetString("Requestsuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search By:.
        /// </summary>
        public static string SearchBy_ {
            get {
                return ResourceManager.GetString("SearchBy:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Provider :.
        /// </summary>
        public static string SearchProvider_ {
            get {
                return ResourceManager.GetString("SearchProvider:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search User:.
        /// </summary>
        public static string SearchUser {
            get {
                return ResourceManager.GetString("SearchUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social History.
        /// </summary>
        public static string SocialHistory {
            get {
                return ResourceManager.GetString("SocialHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SpeechNotRecognized.
        /// </summary>
        public static string SpeechNotRecognized {
            get {
                return ResourceManager.GetString("SpeechNotRecognized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSN.
        /// </summary>
        public static string SSN {
            get {
                return ResourceManager.GetString("SSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Time.
        /// </summary>
        public static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Time:.
        /// </summary>
        public static string StartTime_ {
            get {
                return ResourceManager.GetString("StartTime:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop.
        /// </summary>
        public static string Stop {
            get {
                return ResourceManager.GetString("Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to stop_circle.
        /// </summary>
        public static string stop_circle {
            get {
                return ResourceManager.GetString("stop_circle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit.
        /// </summary>
        public static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        public static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Surgical History.
        /// </summary>
        public static string SurgicalHistory {
            get {
                return ResourceManager.GetString("SurgicalHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End time must be after start time..
        /// </summary>
        public static string TimeMismatchError {
            get {
                return ResourceManager.GetString("TimeMismatchError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transcription.
        /// </summary>
        public static string Transcription {
            get {
                return ResourceManager.GetString("Transcription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Treatment Plan.
        /// </summary>
        public static string TreatmentPlan {
            get {
                return ResourceManager.GetString("TreatmentPlan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Access Failure.
        /// </summary>
        public static string UpdateAccessFailure {
            get {
                return ResourceManager.GetString("UpdateAccessFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UploadError.
        /// </summary>
        public static string UploadError {
            get {
                return ResourceManager.GetString("UploadError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Already Exists.
        /// </summary>
        public static string UserAlreadyExists {
            get {
                return ResourceManager.GetString("UserAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        public static string Username {
            get {
                return ResourceManager.GetString("Username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username Required.
        /// </summary>
        public static string UsernameRequired {
            get {
                return ResourceManager.GetString("UsernameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vitals.
        /// </summary>
        public static string Vitals {
            get {
                return ResourceManager.GetString("Vitals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to wav.
        /// </summary>
        public static string wav {
            get {
                return ResourceManager.GetString("wav", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Word.
        /// </summary>
        public static string Word {
            get {
                return ResourceManager.GetString("Word", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
