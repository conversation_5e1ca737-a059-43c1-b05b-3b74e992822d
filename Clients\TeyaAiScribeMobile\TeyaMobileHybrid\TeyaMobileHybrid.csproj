﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
        <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
        <!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
        <!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

        <!-- Note for MacCatalyst:
            The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
            When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
            The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
            either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
        <!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

        <OutputType>Exe</OutputType>
        <RootNamespace>TeyaMobileHybrid</RootNamespace>
        <UseMaui>true</UseMaui>
        <SingleProject>true</SingleProject>
        <ImplicitUsings>enable</ImplicitUsings>
        <EnableDefaultCssItems>false</EnableDefaultCssItems>
        <Nullable>enable</Nullable>

        <!-- Display name -->
        <ApplicationTitle>TeyaMobileHybrid</ApplicationTitle>

        <!-- App Identifier -->
        <ApplicationId>com.companyname.teyamobilehybrid</ApplicationId>

        <!-- Versions -->
        <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
        <ApplicationVersion>1</ApplicationVersion>

        <!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
        <WindowsPackageType>None</WindowsPackageType>

        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">26.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
        <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>

		<AndroidMinSdkVersion>26</AndroidMinSdkVersion>
		<AndroidTargetSdkVersion>34</AndroidTargetSdkVersion>
		<NeutralLanguage>en-US</NeutralLanguage>
    </PropertyGroup>

    <ItemGroup>
        <!-- App Icon -->
        <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

        <!-- Splash Screen -->
        <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

        <!-- Images -->
        <MauiImage Include="Resources\Images\*" />
        <MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />

        <!-- Custom Fonts -->
        <MauiFont Include="Resources\Fonts\*" />

        <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
        <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="appsettings.json" />
    </ItemGroup>

    <ItemGroup>
      <MauiCss Include="Resources\Styles\app.css" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="CommunityToolkit.Maui" Version="11.2.0" />
        <PackageReference Include="CommunityToolkit.Maui.Core" Version="11.2.0" />
        <PackageReference Include="CommunityToolkit.Maui.MediaElement" Version="6.0.2" />
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="DotNetEnv" Version="3.1.1" />
        <PackageReference Include="Microsoft.Identity.Client" Version="4.71.2-preview" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.51" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.51" />
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
        <PackageReference Include="Syncfusion.Licensing" Version="29.1.41" />
        <PackageReference Include="Syncfusion.Maui.Core" Version="29.1.41" />
        <PackageReference Include="Syncfusion.Maui.Scheduler" Version="29.1.41" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.9.0" />
    </ItemGroup>

    <ItemGroup>
      <MauiXaml Update="AppointmentsDoctor.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
      <MauiXaml Update="AppShell.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
      <MauiXaml Update="Message.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
      <MauiXaml Update="Resources\Styles\Colors.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
      <MauiXaml Update="Resources\Styles\Styles.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
      <MauiXaml Update="SignIn.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
      <MauiXaml Update="TemplatesPage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
      <MauiXaml Update="TeyaAiNew.xaml">
        <Generator>MSBuild:Compile</Generator>
      </MauiXaml>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Components\" />
    </ItemGroup>

    <ItemGroup>
      <MauiAsset Include="appsettings.json" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\TeyaMobileModel\TeyaMobileModel.csproj" />
      <ProjectReference Include="..\TeyaMobileViewModel\TeyaMobileViewModel.csproj" />
    </ItemGroup>

</Project>
