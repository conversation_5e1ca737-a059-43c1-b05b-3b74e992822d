using Syncfusion.Licensing;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Identity.Web;
using MudBlazor.Services;
using Syncfusion.Blazor;
using TeyaWebApp.Components;
using TeyaWebApp.ViewModel;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.HttpOverrides;
using Blazored.SessionStorage;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Globalization;
using StackExchange.Redis;
using DotNetEnv;
using Microsoft.Identity.Client;
using Microsoft.AspNetCore.DataProtection;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.ViewModel;
using BusinessLayer.Services;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Localization;
using TeyaWebApp.TeyaAIScribeResource;
using TeyaUIModels.Model;
using Blazored.LocalStorage;
using TeyaWebApp.Services;
using TeyaUIViewModels.ViewModels;
using Microsoft.AspNetCore.Authorization;
using TeyaWebApp.Authorization;

using System.Text.Json.Serialization;
using System.Text.Json;
var builder = WebApplication.CreateBuilder(args);
builder.Services.AddLogging();
builder.Services.AddLocalization();
builder.Services.AddDataProtection();
Env.Load();
builder.Services.AddServerSideBlazor();
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();
builder.Services.AddScoped<HtmlRenderer>();
builder.Services.AddScoped<RoleMappingState>();
builder.Services.AddScoped<RazorComponentRenderer>();
builder.Services.AddScoped<GraphApiService>();
builder.Services.AddBlazoredSessionStorage();
builder.Services.AddHttpClient<IProductService, ProductService>();
builder.Services.AddMudServices();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();
builder.Services.AddSyncfusionBlazor();
builder.Services.AddScoped<IReviewOfSystemService,ReviewOfSystemService>();
builder.Services.AddScoped<IPageRoleMappingService, PageRoleMappingService>();
builder.Services.AddScoped<IPreDefinedPageRoleMappingService, PreDefinedPageRoleMappingService>();
builder.Services.AddScoped<IPagePathService, PagePathService>();
builder.Services.AddScoped<IMemberService, MemberService>();
builder.Services.AddScoped<IDiagnosticImagingService, DiagnosticImagingService>();
builder.Services.AddScoped<IDiagnosticImagingPageService, DiagnosticImagingPageService>();
builder.Services.AddScoped<ICountryService, countryService>();


builder.Services.AddScoped<IUserLicenseService, UserLicenseService>();
builder.Services.AddScoped<IPlanTypeService, PlanTypeService>();
builder.Services.AddScoped<IGraphAdminService, GraphAdminService>();
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("AppointmentsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Appointments")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("ChartAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Chart")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("configAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/config")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("CreatememberAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Createmember")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("ClaimsLookupAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/ClaimsLookup")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("inviteuserAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/inviteuser")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("LicenseActivationAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/LicenseActivation")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("licenseAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/License")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("productfeaturesettingsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/ProductFeatureSettings")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("manageprofileAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/manageprofile")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("NotesAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Notes")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("VisitAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Visit")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("patientsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/patients")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("PlanBillingAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/PlanBilling")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("PracticeAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Practice")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("ProvidersAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Providers")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("SecurityAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/Security")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("sendmailAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/sendmail")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("templatesAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/templates")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("updateuserAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/updateuser")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("usermanagementAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/usermanagement")));
});
builder.Services.AddScoped<IAuthorizationHandler, DynamicRoleAuthorizationHandler>();

builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(60);  // Adjust timeout as needed
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
})
.AddCookie()
.AddOpenIdConnect(options =>
{
    options.ClientId = Environment.GetEnvironmentVariable("AUTH_CLIENT_ID");
    options.Authority = Environment.GetEnvironmentVariable("AUTH_AUTHORITY");
    options.ClientSecret = Environment.GetEnvironmentVariable("AUTH_CLIENT_SECRET");
    options.ResponseType = Environment.GetEnvironmentVariable("AUTH_RESPONSE_TYPE");
    options.SaveTokens = bool.Parse(Environment.GetEnvironmentVariable("AUTH_SAVE_TOKENS") ?? "false");
    options.CallbackPath = Environment.GetEnvironmentVariable("AUTH_CALLBACK_PATH");
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH_SCOPE_0"));
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH_SCOPE_1"));
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH_SCOPE_2"));

    options.TokenValidationParameters = new TokenValidationParameters
    {
        NameClaimType = "name",
        RoleClaimType = "roles"
    };
    options.Events = new OpenIdConnectEvents
    {
        OnRedirectToIdentityProviderForSignOut = context =>
        {
            var logoutUri = Environment.GetEnvironmentVariable("AUTH_LOGOUT_URI");
            var postLogoutUri = Environment.GetEnvironmentVariable("AUTH_POST_LOGOUT_URI");

            if (!string.IsNullOrEmpty(postLogoutUri))
            {
                logoutUri += $"?post_logout_redirect_uri={Uri.EscapeDataString(postLogoutUri)}";
            }

            context.Response.Redirect(logoutUri);
            context.HandleResponse();


            return Task.CompletedTask;
        }
    };

    options.Events = new OpenIdConnectEvents
    {
        OnTokenResponseReceived = async context =>
        {
            var session = context.HttpContext.Session;

            var confidentialClientApp = ConfidentialClientApplicationBuilder
                .Create(options.ClientId)
                .WithClientSecret(options.ClientSecret)
                .WithAuthority(new Uri(options.Authority))
                .Build();

            var userAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                new[] { Environment.GetEnvironmentVariable("AUTH_SCOPE_0") },
                new UserAssertion(context.TokenEndpointResponse.AccessToken)
            ).ExecuteAsync();

            var graphAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                new[] { Environment.GetEnvironmentVariable("AUTH_SCOPE_4") },
                new UserAssertion(context.TokenEndpointResponse.AccessToken)
            ).ExecuteAsync();
            
            // ? Store tokens in Session Storage
            session.SetString("AccessToken", userAccessTokenResult.AccessToken);
            session.SetString("AccessToken2", graphAccessTokenResult.AccessToken);
        }
    };
});
builder.Services.Configure<JsonSerializerOptions>(options =>
{
    options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.Converters.Add(new ActiveUserConverter());
});
builder.Services.AddServerSideBlazor()
    .AddHubOptions(options => {
        options.ClientTimeoutInterval = TimeSpan.FromSeconds(60);
        options.HandshakeTimeout = TimeSpan.FromSeconds(30);
        options.MaximumReceiveMessageSize = 32 * 1024 * 1024; // 32MB
    });

builder.Services.AddScoped<IFDBService, FDBService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<IAllergyService, AllergyService>();
builder.Services.AddScoped<IMedicalHistoryService, MedicalHistoryService>();
builder.Services.AddScoped<ICommunicationService, CommunicationService>();
builder.Services.AddScoped<IAddressService, AddressService>();
builder.Services.AddScoped<IGuardianService, GuardianService>();
builder.Services.AddScoped<IEmployerService, EmployerService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IOrganizationService, OrganizationService>();
builder.Services.AddScoped<IUserTypeService, UserTypeService>();
builder.Services.AddScoped<IHistoryOfPresentIllnessService, HistoryOfPresentIllnessService>();
builder.Services.AddScoped<ISymptomsService, SymptomsService>();
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddScoped<IChartService, ChartService>();
builder.Services.AddScoped<IUpToDateService, UpToDateService>();
builder.Services.AddScoped<IInsuranceService, InsuranceService>();
builder.Services.AddScoped<ActiveUser>();
builder.Services.AddScoped<PatientService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<IUserThemeService, UserThemeService>();
builder.Services.AddScoped<StateContainer>();
builder.Services.AddScoped<IPageRoleMappingService, PageRoleMappingService>();
builder.Services.AddScoped<IHospitalizationRecordService, HospitalizationRecordService>();
builder.Services.AddScoped<IMemberService, MemberService>();
builder.Services.AddScoped<IICDService, ICDService>();
builder.Services.AddScoped<ITherapeuticInterventionsListService, TherapeuticInterventionsListService>();
builder.Services.AddScoped<ISoapNotesComponentsService, SoapNotesComponentsService>();
builder.Services.AddScoped<ISurgicalService, SurgicalService>();
builder.Services.AddScoped<ICustomImmunizationAlertService, CustomImmunizationAlertService>();

builder.Services.AddScoped<IVaccineService, VaccineService>();
builder.Services.AddScoped<IImmunizationService, ImmunizationService>();
builder.Services.AddScoped<IPhysicalService, PhysicalService>();
builder.Services.AddScoped<IPredefinedVisitTypeService, PredefinedVisitTypeService>();
builder.Services.AddScoped<IRoleslistService, RoleslistService>();
builder.Services.AddScoped<IPastResultService, PastResultService>();
builder.Services.AddScoped<IAppointmentService, AppointmentService>();
builder.Services.AddScoped<IOrganizationService, OrganizationService>();
builder.Services.AddScoped<IProductOrganizationMappingService, ProductOrganizationMappingService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IFacilityService, FacilityService>();
builder.Services.AddScoped<IProductLicenseService, ProductLicenseService>();
builder.Services.AddScoped<IPracticeService, PracticeService>();
builder.Services.AddScoped<IOfficeVisitService, OfficeVisitService>();
builder.Services.AddScoped<ICacheService, RedisCacheService>();
builder.Services.AddScoped<IVisitTypeService, VisitTypeService>();
builder.Services.AddScoped<IVisitStatusService, VisitStatusService>();
builder.Services.AddScoped<ISocialHistoryService, SocialHistoryService>();
builder.Services.AddScoped<IVitalService, VitalService>();
builder.Services.AddScoped<IReferralOutgoingService, ReferralOutgoingService>();
builder.Services.AddScoped<IRxNormService, RxNormService>();
builder.Services.AddScoped<IOrderSetService, OrderSetService>();
builder.Services.AddScoped<ICurrentMedicationService, CurrentMedicationService>();
builder.Services.AddScoped<IPrescriptionMedicationService, PrescriptionMedicationService>();
builder.Services.AddScoped<IFamilyMemberService, FamilyMemberService>();
builder.Services.AddScoped<IRelationService, RelationService>();
builder.Services.AddScoped<SpeechService>();
builder.Services.AddScoped<ICPTService, CPTService>();
builder.Services.AddScoped<IMeasureService, MeasureService>();
builder.Services.AddScoped<IProductFeatureService, ProductFeatureService>();
builder.Services.AddHttpClient<ISpeechService, SpeechService>(client =>
{
    client.BaseAddress = new Uri("http://localhost/TeyaWebApi/");
});
builder.Services.AddScoped<InviteMailParametersService>();
builder.Services.AddRazorPages();
builder.Services.AddHttpContextAccessor();
builder.Services.AddSyncfusionBlazor();
builder.Services.Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
});
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder =>
        {
            builder.AllowAnyOrigin()
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
});
builder.Services.AddMudServices();
builder.Configuration.AddEnvironmentVariables();
builder.Services.AddRedisCache(builder.Configuration);
builder.Services.AddSyncfusionBlazor();
builder.Services.AddScoped<ICacheService, RedisCacheService>();
Env.Load();

builder.Services.AddScoped<IChiefComplaintService, ChiefComplaintService>();
builder.Services.AddScoped<ITemplateService, TemplateService>();
builder.Services.AddScoped<IVisionExaminationService, VisionExaminationService>();
builder.Services.AddScoped<IPredefinedTemplateService, PredefinedTemplateService>();
builder.Services.AddScoped<IAssessmentsService, AssessmentsService>();
builder.Services.AddScoped<ITherapeuticInterventionsService, TherapeuticInterventionsService>();
builder.Services.AddScoped<IPhysicalTherapyService, PhysicalTherapyService>();
builder.Services.AddScoped<IProcedureService, ProcedureService>();
builder.Services.AddScoped<SharedNotesService>();
builder.Services.AddScoped<IObHistoryService, ObHistoryService>();
builder.Services.AddScoped<IGynHistoryService, GynHistoryService>();
builder.Services.AddScoped<IExaminationService, ExaminationService>();
builder.Services.AddScoped<IProgressNotesService, ProgressNotesService>();
builder.Services.AddScoped<ILabTestsService,LabTestService>();
builder.Services.AddScoped<IBillingService, BillingService>();
builder.Services.AddScoped<IProductFeatureService, ProductFeatureService>();
builder.Services.AddScoped<ICustomLabAlertService, CustomLabAlertService>();
builder.Services.AddScoped<IPatientSpecificAlertsService, PatientSpecificAlertsService>();
builder.Services.AddScoped<IAlertService,AlertService>();

builder.Services.AddScoped<ICustomProcedureAlertService, CustomProcedureAlertService>();
builder.Services.AddScoped<IDiagnosticImagingAlertService, DiagnosticImagingAlertService>();
builder.Services.AddScoped<IDXAlertService, DXAlertService>();

Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(Environment.GetEnvironmentVariable("SyncfusionKey"));

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseForwardedHeaders();
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseForwardedHeaders();
    app.UseHttpsRedirection();
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
});
app.UseSession();
app.UseCors("AllowAll");
app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseAntiforgery();
app.UseAuthentication();
app.UseAuthorization();
app.UseForwardedHeaders();

app.Use((context, next) =>
{
    if (context.Request.Headers.TryGetValue("X-Forwarded-PathBase", out StringValues pathBase))
    {
        context.Request.PathBase = new PathString(pathBase);
    }

    if (context.Request.Headers.TryGetValue("X-Forwarded-Proto", out StringValues proto))
    {
        context.Request.Protocol = proto;
    }

    return next();
});

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode();

app.MapGet("/authentication/logout", async context =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignOutAsync(OpenIdConnectDefaults.AuthenticationScheme);
    context.Response.Cookies.Delete(".AspNetCore.Identity.Application");
});

app.MapGet("/authentication/login", async context =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignOutAsync(OpenIdConnectDefaults.AuthenticationScheme);
    context.Response.Cookies.Delete(".AspNetCore.Identity.Application");

    context.Response.Redirect("/");
});
app.Run();


public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRedisCache(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IConnectionMultiplexer>(sp =>
        {
            var connectionString = Environment.GetEnvironmentVariable("RedisConnectionString");
            return ConnectionMultiplexer.Connect(connectionString);
        });

        services.AddSingleton<ICacheService, RedisCacheService>();

        return services;
    }
}
