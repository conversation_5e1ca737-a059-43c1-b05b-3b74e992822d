﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IPredefinedTemplateService
    {
        Task<List<TemplateData>> GetTemplatesAsync();
        Task CreateTemplatesAsync(List<TemplateData> templates);
        Task DeleteTemplatesAsync(Guid templateId);
        Task UpdateTemplatesAsync(TemplateData templates);
    }
}