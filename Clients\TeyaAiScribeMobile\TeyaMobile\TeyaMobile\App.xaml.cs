﻿using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
namespace TeyaMobile
{
    public partial class App : Application
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly NavigationManager _navigationManager;
        private readonly ILogger<App>? _logger;
        public App(IServiceProvider serviceProvider, NavigationManager navigationManager)
        {
            _serviceProvider = serviceProvider;
            _navigationManager = navigationManager;
            InitializeComponent();
        }

        protected override Window CreateWindow(IActivationState? activationState)
        {
            return new Window(new AppShell()) { Title = "Teya AI Scribe" };
        }

        public static Page GetRootPage()
        {
            if (Current?.Windows?.Count > 0 && Current.Windows[0].Page is NavigationPage navigationPage)
            {
                return navigationPage.RootPage;
            }
            return null;
        }
    }
}
