﻿using System.Text.Json.Serialization;

namespace TeyaMobileModel.Model
{
    public class Speech : IModel
    {
        [JsonPropertyName("Result")]
        public string Result { get; set; }

        [JsonPropertyName("Timestamps")]
        public List<WordTiming> Timestamps { get; set; } = new List<WordTiming>();

        [JsonPropertyName("TranscribedData")]
        public string TranscribedData { get; set; } 
    }
}
