﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface ISurgicalService
    {
        Task<List<Surgical>> GetSurgeriesByIdAsync(Guid id);
        Task AddSurgeryAsync(List<Surgical> surgeries);
        Task DeletesurgeryAsync(Surgical surgery);
        Task UpdateSurgeryAsync(Surgical surgeries);
        Task<List<Surgical>> GetSurgeryByIdAsyncAndIsActive(Guid id);
        Task UpdateSurgeryListAsync(List<Surgical> surgery);
    }
}
