﻿using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.TeyaUIViewModelResources;

namespace TeyaMobileViewModel.ViewModel
{
    public class PredefinedTemplateService : IPredefinedTemplateService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public PredefinedTemplateService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<TemplateData>> GetTemplatesAsync()
        {
            var Url = $"{_EncounterNotes}/api/PredefinedTemplates";
            var accessToken = _tokenService.AccessToken;
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, Url);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<TemplateData>>();
            }
            else
            {
                return null;
            }
        }
        public async Task CreateTemplatesAsync(List<TemplateData> templates)
        {
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(templates);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var Url = $"{_EncounterNotes}/api/PredefinedTemplates/Templates";
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, Url)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                throw new HttpRequestException(_localizer["TemplateRetrievalFailure"]);
            }
        }
        public async Task UpdateTemplatesAsync(TemplateData templates)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_EncounterNotes}/api/PredefinedTemplates/{templates.Id}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(templates);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }
        public async Task DeleteTemplatesAsync(Guid templateId)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/PredefinedTemplates/{templateId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }
    }
}