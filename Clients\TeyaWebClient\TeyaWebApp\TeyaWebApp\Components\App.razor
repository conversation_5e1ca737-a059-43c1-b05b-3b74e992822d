﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<App> localizer

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link href="../Components/GenericFontComponents/typography-theme.css" rel="stylesheet" />
    <link rel="stylesheet" href="./app.css" />
    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="TeyaWebApp.styles.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <link rel="stylesheet" href="_content/Syncfusion.Blazor/styles/material.css" />
    <!-- wwwroot/home.html -->

    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet" />


    <link href="_content/MudBlazor/MudBlazor.min.css?version=0.1" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap5.css" rel="stylesheet" />
    <MudThemeProvider />
    <MudDialogProvider />
    <HeadOutlet />
</head>

<body>
    <Routes @rendermode="new InteractiveServerRenderMode(prerender: false)" />
    <script src="_framework/blazor.web.js"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="scripts/AudioRecorder.js"></script>
    <script>navigator.serviceWorker.register('service-worker.js');</script>
    <script src="scripts/audioHighlight.js"></script>
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script src="_content/Syncfusion.Blazor/scripts/syncfusion-blazor.min.js"></script>
    <link href="_content/Syncfusion.Blazor/styles/bootstrap5.css" rel="stylesheet" />
    <script src="_content/Syncfusion.Blazor/scripts/syncfusion-blazor.min.js"></script>

    <script>
        window.Localization = {
            uploadError: '@(localizer["UploadError"])',
            uploadSuccessful: '@(localizer["UploadSuccessful"])'
        };
    </script>
    <script>
        window.AppSettings = {
        uploadUrl: '@(Environment.GetEnvironmentVariable("EncounterNotesURL"))/Speech/upload'
                };
    </script>


</body>

</html>
