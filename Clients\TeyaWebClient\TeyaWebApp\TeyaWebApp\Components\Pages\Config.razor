﻿@page "/config"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "configAccessPolicy")]
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Buttons
@using TeyaUIModels.Model
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@inject IDialogService DialogService

<MudTabs Elevation="2" Rounded="true" @bind-ActivePanelIndex="activeTabIndex">
	<MudTabPanel Text="Organization" Class="@GetTabClass(0)">
		<GenericCard Heading="@Localizer["Organization"]">
			<MudPaper Elevation="2" Class="p-4 mb-4">
				<MudGrid Spacing="2" >
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="Organization Name"
											  @bind-Value="Organization.OrganizationName"
											  Required="true"
											  Disabled="true" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudSelect Label="Country" @bind-Value="Organization.Country" Required="true">
									@foreach (var country in Countries)
									{
										<MudSelectItem Value="@country">@country</MudSelectItem>
									}
								</MudSelect>
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="Address" @bind-Value="Organization.Address" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="Contact Number" @bind-Value="Organization.ContactNumber" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="Email" @bind-Value="Organization.Email" InputType="MudBlazor.InputType.Email" />
							</MudItem>
						</MudGrid>
					</MudItem>
				</MudGrid>
				<MudGrid Spacing="2" Class="mt-4 justify-end">
					<MudItem xs="6" sm="4" md="3" lg="2">
						<MudButton Variant="Variant.Text" Color="Color.Secondary" OnClick="CancelOrganizationForm" FullWidth="true" Class="mr-2">Clear</MudButton>
					</MudItem>
					<MudItem xs="6" sm="4" md="3" lg="2">
						<MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveOrganizationForm" FullWidth="true">Save</MudButton>
					</MudItem>
				</MudGrid>
			</MudPaper>
			<MudDivider />
			<SfGrid DataSource="@ActiveOrganization" AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both" @ref="organizationGrid">
				<GridPageSettings PageSize="10"></GridPageSettings>
				<GridColumns>
					<GridColumn Field="@nameof(Organization.OrganizationId)" HeaderText="Organization ID" TextAlign="TextAlign.Center" Visible="false"></GridColumn>
					<GridColumn Field="@nameof(Organization.OrganizationName)" HeaderText="Organization Name" TextAlign="TextAlign.Center"></GridColumn>
					<GridColumn Field="@nameof(Organization.Country)" HeaderText="Country" TextAlign="TextAlign.Center"></GridColumn>
					<GridColumn Field="@nameof(Organization.Address)" HeaderText="Address" TextAlign="TextAlign.Center"></GridColumn>
					<GridColumn Field="@nameof(Organization.CreatedDate)" HeaderText="Created Date" TextAlign="TextAlign.Center" Format="d" Type="ColumnType.Date"></GridColumn>
				</GridColumns>
			</SfGrid>
		</GenericCard>
	</MudTabPanel>
	<MudTabPanel Text="Facilities" Class="@GetTabClass(1)">
		<GenericCard Heading="@Localizer["Facilities"]">
			<MudPaper Elevation="2" Class="p-4 mb-4">
				<MudGrid Spacing="2">
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudSelect Label="Organization" @bind-Value="Facility.OrganizationId" Required="true">
									@foreach (var org in ActiveOrganization)
									{
										<MudSelectItem Value="@org.OrganizationId">@org.OrganizationName</MudSelectItem>
									}
								</MudSelect>
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="Facility Name" @bind-Value="Facility.FacilityName" Required="true" @ref="facilityNameInput" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="Street Name" @bind-Value="Facility.StreetName" Required="true" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="City" @bind-Value="Facility.City" Required="true" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="Zip Code" @bind-Value="Facility.Zipcode" Required="true" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudTextField Label="State" @bind-Value="Facility.State" Required="true" />
							</MudItem>
						</MudGrid>
					</MudItem>
					<MudItem xs="12">
						<MudGrid Spacing="2">
							<MudItem xs="12" sm="6" md="4" lg="3">
								<MudSelect Label="Country" @bind-Value="Facility.Country" Required="true">
									@foreach (var country in Countries)
									{
										<MudSelectItem Value="@country">@country</MudSelectItem>
									}
								</MudSelect>
							</MudItem>
						</MudGrid>
					</MudItem>
				</MudGrid>
				<MudGrid Spacing="2" Class="mt-4 justify-end">
					<MudItem xs="6" sm="4" md="3" lg="2">
						<MudButton Variant="Variant.Text" Color="Color.Secondary" OnClick="CancelFacilityForm" FullWidth="true" Class="mr-2">Clear</MudButton>
					</MudItem>
					<MudItem xs="6" sm="4" md="3" lg="2">
						<MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveFacilityAsync" FullWidth="true">Add</MudButton>
					</MudItem>
				</MudGrid>
			</MudPaper>
			<SfGrid DataSource="@ActiveUserFacilities" AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both" @ref="facilityGrid">
				<GridPageSettings PageSize="10"></GridPageSettings>
				<GridColumns>
					<GridColumn Type="ColumnType.CheckBox" Width="50"></GridColumn>
					<GridColumn Field="@nameof(Facility.FacilityName)" HeaderText="Facility Name" TextAlign="TextAlign.Center"></GridColumn>
					<GridForeignColumn TValue="Organization" Field="OrganizationId" AllowEditing="false" HeaderText="Organization Name" ForeignKeyField="OrganizationId"
									   ForeignKeyValue="OrganizationName" ForeignDataSource="ActiveOrganization" TextAlign="TextAlign.Center" Width="150" />
					<GridColumn Field="@nameof(Facility.CreatedDate)" HeaderText="Created Date" TextAlign="TextAlign.Center" Format="d" Type="ColumnType.Date"></GridColumn>
				</GridColumns>
			</SfGrid>
			<MudGrid Class="mt-4 justify-end">
				<MudItem xs="6" sm="4" md="3" lg="2">
					<MudButton Variant="Variant.Filled" Color="Color.Error" OnClick="ShowDeleteConfirmationForFacility" FullWidth="true" Class="mr-4">Delete</MudButton>
				</MudItem>
			</MudGrid>
		</GenericCard>
	</MudTabPanel>
	<MudTabPanel Text="Role" Class="@GetTabClass(2)">
		<MudTabs>
			<MudTabPanel Text="Role">
				<MudPaper Elevation="2" Class="p-4 mb-4">
					<MudGrid Spacing="2">
						<MudItem xs="12">
							<MudGrid Spacing="2">
								<MudItem xs="12" sm="6" md="4" lg="3">
									<MudSelect Label="Organization" @bind-Value="Role.OrganizationID" Required="true">
										@foreach (var org in ActiveOrganization)
										{
											<MudSelectItem Value="@org.OrganizationId">@org.OrganizationName</MudSelectItem>
										}
									</MudSelect>
								</MudItem>
							</MudGrid>
						</MudItem>
						<MudItem xs="12">
							<MudGrid Spacing="2">
								<MudItem xs="12" sm="6" md="4" lg="3">
									<MudTextField Label="Role Name" @bind-Value="Role.RoleName" Required="true" @ref="roleNameInput" />
								</MudItem>
							</MudGrid>
						</MudItem>
					</MudGrid>
					<MudGrid Spacing="2" Class="mt-4 justify-end">
						<MudItem xs="6" sm="4" md="3" lg="2">
							<MudButton Variant="Variant.Text" Color="Color.Secondary" OnClick="CancelRoleForm" FullWidth="true" Class="mr-2">Clear</MudButton>
						</MudItem>
						<MudItem xs="6" sm="4" md="3" lg="2">
							<MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveRoleAsync" FullWidth="true">Add</MudButton>
						</MudItem>
					</MudGrid>
				</MudPaper>
				<MudDivider />
				<SfGrid DataSource="@ActiveRoles" AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both" @ref="roleGrid">
					<GridPageSettings PageSize="10"></GridPageSettings>
					<GridColumns>
						<GridColumn Type="ColumnType.CheckBox" Width="50"></GridColumn>
						<GridColumn Field="@nameof(Role.RoleName)" HeaderText="Role Name" TextAlign="TextAlign.Left"></GridColumn>
						<GridForeignColumn TValue="Organization" Field="OrganizationId" AllowEditing="false" HeaderText="Organization Name" ForeignKeyField="OrganizationId"
										   ForeignKeyValue="OrganizationName" ForeignDataSource="ActiveOrganization" TextAlign="TextAlign.Center" Width="150" />
						<GridColumn Field="@nameof(Role.CreatedDate)" HeaderText="Role Created Date" TextAlign="TextAlign.Center" Format="d" Type="ColumnType.Date"></GridColumn>
					</GridColumns>
				</SfGrid>
				<MudGrid Class="mt-4 justify-end">
					<MudItem xs="6" sm="4" md="3" lg="2">
						<MudButton Variant="Variant.Filled" Color="Color.Error" OnClick="ShowDeleteConfirmationForRole" FullWidth="true" Class="mr-4">Delete</MudButton>
					</MudItem>
				</MudGrid>
			</MudTabPanel>

			<MudTabPanel Text="Permissions">
				<MudPaper Elevation="2" Class="p-4 mb-4">
					<MudGrid Spacing="2">
						<MudContainer>
							<MudCard>
								<MudCardContent>
									<MudForm Model="this" @ref="form">
										<MudGrid>
											<MudItem xs="12" md="6" lg="3">
												<MudSelect Label="@Localizer["Role"]" @bind-Value="selectedRole" Required="true">
													@foreach (var role in ActiveRoles)
													{
														<MudSelectItem Value="@role">@role.RoleName</MudSelectItem>
													}
												</MudSelect>
											</MudItem>
											<MudItem xs="12" md="6" lg="3">
												<MudTextField Label="@Localizer["Organization"]" Value="@activeOrganization.OrganizationName" ReadOnly="true" />
											</MudItem>
										</MudGrid>
									</MudForm>
								</MudCardContent>
							</MudCard>

							<MudCard Class="mt-4">
								<MudCardContent>
									<SfGrid DataSource="@PageUrls" AllowPaging="true" AllowSorting="true" @ref="pageRoleMappingGrid" CssClass="compact-grid">
										<GridPageSettings PageSize="20"></GridPageSettings>
										<GridColumns>
											<GridColumn Field="PagePath" HeaderText="@Localizer["Page URL"]" TextAlign="TextAlign.Left" Width="50%" />
											<GridColumn HeaderText="@Localizer["Access"]" TextAlign="TextAlign.Left" Width="50%">
												<Template>
													<div class="access-checkbox">
														<SfCheckBox @bind-Checked="((PageRoleMappingData)context).HasAccess"
																	@onchange="(e) => OnCheckboxChanged((PageRoleMappingData)context)" />
													</div>
												</Template>
											</GridColumn>
										</GridColumns>
									</SfGrid>
									<MudGrid Class="mt-4 justify-end">
										<MudItem xs="6" sm="4" md="3" lg="2">
											<MudButton Variant="Variant.Outlined" Color="Color.Secondary" OnClick="CancelChanges" FullWidth="true" Class="mr-4">
												@Localizer["Cancel"]
											</MudButton>
										</MudItem>
										<MudItem xs="6" sm="4" md="3" lg="2">
											<MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SaveSelectedPageRoleMappings" FullWidth="true">
												@Localizer["Save"]
											</MudButton>
										</MudItem>
									</MudGrid>
								</MudCardContent>
							</MudCard>
						</MudContainer>
					</MudGrid>
				</MudPaper>
				
			</MudTabPanel>
		</MudTabs>

	</MudTabPanel>
	<MudTabPanel Text="Visit Type" Class="@GetTabClass(3)">
		<GenericCard Heading="@Localizer["Visit Type"]">
			<MudPaper Elevation="2" Class="p-4 mb-4">
				<MudGrid Class="mt-4">
					<MudItem xs="12">
						<SfGrid @ref="visitTypeGrid"
								DataSource="@VisitTypes"
								TValue="VisitType"
								AllowPaging="true"
								AllowSorting="true"
								GridLines="GridLine.Both"
								Toolbar="@(new List<string>() { @Localizer["Add"] })">
							<GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" AllowEditOnDblClick="true"></GridEditSettings>
							<GridPageSettings PageSize="10"></GridPageSettings>
							<GridEvents OnActionComplete="OnGridAction" TValue="VisitType"></GridEvents>
							<GridColumns>
								<GridColumn Field="ID" IsPrimaryKey="true" Visible="false"></GridColumn>
								<GridColumn Field=@Localizer["VisitName"] HeaderText="Visit Name"
											TextAlign="TextAlign.Left" AllowEditing="false">
								</GridColumn>
								<GridColumn Field=@Localizer["CPTCode"] HeaderText="CPT Code"
											TextAlign="TextAlign.Left" AllowEditing="true">
								</GridColumn>
								<GridColumn HeaderText="@Localizer["Actions"]"
											TextAlign="TextAlign.Center" Width="100">
									<GridCommandColumns>
										<GridCommandColumn Type="CommandButtonType.Delete"
														   ButtonOption="@(new CommandButtonOptions() {
														   IconCss = "e-icons e-delete",
														   CssClass = "e-flat" })" />
									</GridCommandColumns>
								</GridColumn>
							</GridColumns>
						</SfGrid>
					</MudItem>
				</MudGrid>
			</MudPaper>
		</GenericCard>
	</MudTabPanel>
</MudTabs>

@code {
	private int activeTabIndex = 0;

	private string GetTabClass(int tabIndex) => activeTabIndex == tabIndex ? "active-tab" : string.Empty;
}