﻿using System.Text.Json.Serialization;

namespace TeyaMobileModel.Model
{
    public class ProductLicense : IModel
    {
        [JsonPropertyName("Id")]
        public Guid Id { get; set; }

        [JsonPropertyName("ProductName")]
        public string? ProductName { get; set; }

        [JsonPropertyName("Description")]
        public string? Description { get; set; }

        [JsonPropertyName("IsLicenseActivated")]
        public bool? IsLicenseActivated { get; set; }
    }
}
