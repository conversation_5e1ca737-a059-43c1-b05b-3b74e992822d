﻿using Microsoft.Extensions.Logging;
using Syncfusion.Maui.Core.Hosting;
using Microsoft.Maui.Controls.Hosting;
using Microsoft.Maui.Hosting;
using Microsoft.Extensions.Logging;
using CommunityToolkit.Maui;
using Plugin.Maui.Audio;
using Syncfusion.Maui.Core.Hosting;
using Syncfusion.Maui.Popup;
using CommunityToolkit.Maui.Core;
using Microsoft.JSInterop;
using TeyaUIViewModels.ViewModel;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Maui.Controls.Hosting;
using Microsoft.Maui.Hosting;
using Syncfusion.Licensing;
using TeyaMobileModel.Model;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Http;
using TeyaMobileViewModel.ViewModel;
using System;
using System.Net.Http;
using Syncfusion.Blazor;

namespace TeyaMobileHybrid
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .ConfigureSyncfusionCore()
                .UseMauiCommunityToolkit()
                .UseMauiCommunityToolkitMediaElement()
                .UseMauiCommunityToolkitCore()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSans");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                    fonts.AddFont("FluentSystemIcons-Filled.ttf", "FluentIcons");
                    fonts.AddFont("MaterialIcons-Regular.ttf", "MaterialIcons");
                })
                .UseMauiCommunityToolkit();

            builder.Services.AddMauiBlazorWebView();

            builder.Logging.ClearProviders();
            builder.Logging.AddDebug();

            builder.Services.AddSingleton<IAudioManager, AudioManager>();

            // Load environment variables from .env file
            DotNetEnv.Env.Load();

            using var stream = FileSystem.OpenAppPackageFileAsync("appsettings.json").Result;
            builder.Configuration.AddJsonStream(stream);

            foreach (var setting in builder.Configuration.AsEnumerable())
            {
                if (!string.IsNullOrEmpty(setting.Key) && setting.Value != null)
                {
                    Environment.SetEnvironmentVariable(setting.Key, setting.Value);
                }
            }

            var syncfusionKey = builder.Configuration["SyncfusionKey"];
            if (!string.IsNullOrEmpty(syncfusionKey))
            {
                SyncfusionLicenseProvider.RegisterLicense(syncfusionKey);
            }

            builder.Services.AddSyncfusionBlazor();
            builder.Services.AddLocalization();
            builder.Services.AddSingleton(AudioManager.Current);
            builder.Services.AddSingleton<IAudioManager, AudioManager>();
            builder.Services.AddSingleton<AppointmentsViewModel>();
            builder.Services.AddTransient<MainPage>();
            builder.Services.AddTransient<TeyaAiNew>();
            builder.Services.AddTransient<Message>();
            builder.Services.AddTransient<TemplatesPage>();
            builder.Services.AddSingleton<NavigationManager>();
            builder.Services.AddTransient<AppointmentsDoctor>();
            builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
            builder.Services.AddSingleton<ISpeechService, SpeechService>();
            builder.Services.AddSingleton<IProgressNotesService, ProgressNotesService>();
            builder.Services.AddSingleton<PatientService>();
            builder.Services.AddSingleton<AppointmentService>();
            builder.Services.AddSingleton<HttpClient>();
            builder.Services.AddSingleton<ActiveUser>();
            

#if DEBUG
            builder.Services.AddBlazorWebViewDeveloperTools();
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
