﻿namespace TeyaMobile
{
    public partial class AppShell : Shell
    {
        public AppShell()
        {
            InitializeComponent();

            //Routing.RegisterRoute(nameof(MainPage), typeof(MainPage));
            //Routing.RegisterRoute(nameof(UserSettings), typeof(UserSettings));
            //Routing.RegisterRoute(nameof(Message), typeof(Message));
            //Routing.RegisterRoute(nameof(TeyaAI), typeof(TeyaAI));
            //Routing.RegisterRoute(nameof(AppointmentsDoctor), typeof(AppointmentsDoctor));
        }
    }
}
