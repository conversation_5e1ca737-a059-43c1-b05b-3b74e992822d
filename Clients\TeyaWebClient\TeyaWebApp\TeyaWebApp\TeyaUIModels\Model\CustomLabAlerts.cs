﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class CustomLabAlerts
    {
        public Guid Id { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid pcpId { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? WebReference { get; set; }
        public int? AgeLowerBound { get; set; }
        public int? AgeUpperBound { get; set; }
        public string? OrderSet { get; set; }
        public string? Gender { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool IsActive { get; set; }
    }
}
