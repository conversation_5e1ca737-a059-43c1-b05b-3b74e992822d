﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaWebApp;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IChiefComplaintService
    {
        Task<List<ChiefComplaint>> GetAllComplaintsAsync();
        Task AddAsync(ChiefComplaintDTO complaintDto);
        Task UpdateComplaintAsync(Guid id, ChiefComplaintDTO complaintDto);
        Task DeleteComplaintAsync(Guid id);

        Task UpdateComplaintListAsync(List<ChiefComplaintDTO> complaints);
        Task<IEnumerable<ChiefComplaint>> GetByPatientIdAsync(Guid patientId);
        Task AddAsync(List<ChiefComplaintDTO> complaintList);
        Task<List<ChiefComplaintDTO>> LoadComplaintsAsync(Guid patientId);
        Task<List<ChiefComplaintDTO>> GetProcessedComplaintsAsync();


    }
}
