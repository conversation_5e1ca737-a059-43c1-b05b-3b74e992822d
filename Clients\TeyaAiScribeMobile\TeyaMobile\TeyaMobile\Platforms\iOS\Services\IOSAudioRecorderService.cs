using AVFoundation;
using Foundation;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Platforms.iOS.Services
{
    public class IOSAudioRecorderService : IAudioRecorder
    {
        private AVAudioRecorder? _audioRecorder;
        private string _filePath = string.Empty;
        private DateTime _startTime;
        private TimeSpan _pausedDuration = TimeSpan.Zero;
        private DateTime? _pauseTime;
        private RecordingState _recordingState = RecordingState.Stopped;
        private NSError? _lastError;
        private NSUrl? _audioFileUrl;
        private readonly ILogger<IOSAudioRecorderService> _logger;
        private Guid? _nextRecordingId;

        public IOSAudioRecorderService(ILogger<IOSAudioRecorderService> logger)
        {
            _logger = logger;
            _logger.LogInformation("IOSAudioRecorderService initialized");
        }

        public RecordingState RecordingState
        {
            get => _recordingState;
            private set
            {
                if (_recordingState != value)
                {
                    _recordingState = value;
                    RecordingStateChanged?.Invoke(this, _recordingState);
                }
            }
        }

        public event EventHandler<RecordingState>? RecordingStateChanged;
        public event EventHandler<Exception>? ErrorOccurred;

        public void SetNextRecordingId(Guid recordingId)
        {
            _nextRecordingId = recordingId;
        }

        private string GetRecordingFileName()
        {
            var ext = ".aac";
            if (_nextRecordingId.HasValue)
            {
                return $"{_nextRecordingId.Value}{ext}";
            }
            return $"recording_{DateTime.Now:yyyyMMdd_HHmmss}{ext}";
        }

        public async Task StartRecordingAsync()
        {
            try
            {
                if (RecordingState == RecordingState.Recording)
                    return;

                var audioSession = AVAudioSession.SharedInstance();
                var permissionGranted = await RequestPermissionAsync();

                if (!permissionGranted)
                {
                    throw new Exception("Permission to record audio was denied");
                }

                audioSession.SetCategory(AVAudioSessionCategory.PlayAndRecord);
                audioSession.SetActive(true);

                string fileName = GetRecordingFileName();

                string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
                _filePath = System.IO.Path.Combine(documentsPath, fileName);
                _audioFileUrl = NSUrl.FromFilename(_filePath);

                var settings = new AudioSettings
                {
                    SampleRate = 16000,
                    Format = AudioToolbox.AudioFormatType.MPEG4AAC,
                    NumberChannels = 1, 
                    AudioQuality = AVAudioQuality.Medium
                };

                _audioRecorder = AVAudioRecorder.Create(_audioFileUrl, settings, out _lastError);

                if (_lastError != null)
                {
                    throw new Exception($"Error creating audio recorder: {_lastError.LocalizedDescription}");
                }

                _audioRecorder.PrepareToRecord();
                _audioRecorder.Record();

                _startTime = DateTime.Now;
                _pausedDuration = TimeSpan.Zero;
                _pauseTime = null;
                RecordingState = RecordingState.Recording;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, ex);
                ReleaseAudioRecorder();
                throw;
            }
        }

        public async Task PauseRecordingAsync()
        {
            if (RecordingState != RecordingState.Recording)
                return;

            try
            {
                await Task.Run(() =>
                {
                    if (_audioRecorder?.Recording == true)
                    {
                        _audioRecorder.Pause();
                        _pauseTime = DateTime.Now;
                        RecordingState = RecordingState.Paused;
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, ex);
                throw;
            }
        }

        public async Task ResumeRecordingAsync()
        {
            if (RecordingState != RecordingState.Paused)
                return;

            try
            {
                await Task.Run(() =>
                {
                    if (_audioRecorder != null)
                    {
                        _audioRecorder.Record();

                        if (_pauseTime.HasValue)
                        {
                            _pausedDuration += DateTime.Now - _pauseTime.Value;
                            _pauseTime = null;
                        }

                        RecordingState = RecordingState.Recording;
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, ex);
                throw;
            }
        }

        public async Task<string> StopRecordingAsync()
        {
            if (RecordingState == RecordingState.Stopped)
                return string.Empty;

            try
            {
                RecordingState = RecordingState.Processing;

                await Task.Run(() =>
                {
                    if (_audioRecorder?.Recording == true || RecordingState == RecordingState.Paused)
                    {
                        _audioRecorder?.Stop();
                    }

                    ReleaseAudioRecorder();
                });

                RecordingState = RecordingState.Stopped;
                return _filePath;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, ex);
                ReleaseAudioRecorder();
                RecordingState = RecordingState.Stopped;
                throw;
            }
        }

        public double GetRecordingDuration()
        {
            if (RecordingState == RecordingState.Stopped)
                return 0;

            if (_audioRecorder != null)
            {
                return _audioRecorder.CurrentTime;
            }

            TimeSpan duration;

            if (_pauseTime.HasValue)
            {
                duration = (_pauseTime.Value - _startTime) - _pausedDuration;
            }
            else
            {
                duration = (DateTime.Now - _startTime) - _pausedDuration;
            }

            return duration.TotalSeconds;
        }

        public bool SupportsPauseResume()
        {
            return true; // iOS supports pause/resume
        }

        public string GetFileExtension()
        {
            return ".aac";
        }

        private async Task<bool> RequestPermissionAsync()
        {
            var tcs = new TaskCompletionSource<bool>();

            AVAudioSession.SharedInstance().RequestRecordPermission(granted =>
            {
                tcs.SetResult(granted);
            });

            return await tcs.Task;
        }

        private void ReleaseAudioRecorder()
        {
            if (_audioRecorder != null)
            {
                _audioRecorder.Dispose();
                _audioRecorder = null;
            }

            // Deactivate audio session
            AVAudioSession.SharedInstance().SetActive(false);
        }
    }
}
