﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IOfficeVisitService
    {
        Task<List<Office_visit_appointments>> GetAppointmentsByuserIdAsync(Guid userid);
        Task<List<Office_visit_members>> GetMembersByUserIdsAsync(List<Guid> patientIds);
        Task<List<OfficeVisitModel>> GetPatientListByIdAsync(Guid userId);
    }
}
