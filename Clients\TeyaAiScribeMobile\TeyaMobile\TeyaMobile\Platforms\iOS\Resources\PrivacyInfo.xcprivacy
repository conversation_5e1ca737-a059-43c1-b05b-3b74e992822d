<?xml version="1.0" encoding="UTF-8"?>
<!--
This is the minimum required version of the Apple Privacy Manifest for .NET MAUI apps.
The contents below are needed because of APIs that are used in the .NET framework and .NET MAUI SDK.

You are responsible for adding extra entries as needed for your application.

More information: https://aka.ms/maui-privacy-manifest
-->
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>NSPrivacyAccessedAPITypes</key>
		<array>
			<!-- Required by .NET MAUI and .NET runtime -->
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>C617.1</string>
				</array>
			</dict>
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategorySystemBootTime</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>35F9.1</string>
				</array>
			</dict>
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryDiskSpace</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>E174.1</string>
				</array>
			</dict>

			<!-- User defaults / preferences -->
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryUserDefaults</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>CA92.1</string>
				</array>
			</dict>

			<!-- Microphone -->
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryMicrophone</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>A123.1</string>
				</array>
			</dict>

			<!-- Networking -->
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryNetworking</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>F254.1</string>
				</array>
			</dict>

			<!-- Speech Recognition -->
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategorySpeechRecognition</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>S124.1</string>
				</array>
			</dict>

			<!-- Location Services -->
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryLocation</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>L142.1</string>
				</array>
			</dict>
		</array>
	</dict>
</plist>

