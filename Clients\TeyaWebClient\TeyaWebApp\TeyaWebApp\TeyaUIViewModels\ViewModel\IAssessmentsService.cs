﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IAssessmentsService
    {
        Task<List<AssessmentsData>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<AssessmentsData>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<string>> GetDiagnosisListByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddAssessmentsAsync(List<AssessmentsData> medicalHistories, Guid? OrgID, bool Subscription);
        Task UpdateAssessmentsAsync(AssessmentsData _Assessments, Guid? OrgID, bool Subscription);
        Task UpdateAssessmentsListAsync(List<AssessmentsData> medicalHistories, Guid? OrgID, bool Subscription);
        Task DeleteAssessmentsByEntityAsync(AssessmentsData _Assessments, Guid? OrgID, bool Subscription);
        Task<Dictionary<string, List<string>>> GetAllMedicationsRelatedToAssessments(Guid PatientId, Guid? OrgID, bool Subscription);
    }
}
