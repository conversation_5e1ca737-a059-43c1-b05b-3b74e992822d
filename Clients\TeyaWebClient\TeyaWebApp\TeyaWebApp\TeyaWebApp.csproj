﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>6234712a-5c3a-480a-9fdf-5b3e3febb99b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <Optimize>False</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="TeyaUIModels\**" />
    <Compile Remove="TeyaUIViewModels\**" />
    <Compile Remove="wwwroot\NewFolder1\**" />
    <Compile Remove="wwwroot\NewFolder\**" />
    <Content Remove="TeyaUIModels\**" />
    <Content Remove="TeyaUIViewModels\**" />
    <Content Remove="wwwroot\NewFolder1\**" />
    <Content Remove="wwwroot\NewFolder\**" />
    <EmbeddedResource Remove="TeyaUIModels\**" />
    <EmbeddedResource Remove="TeyaUIViewModels\**" />
    <EmbeddedResource Remove="wwwroot\NewFolder1\**" />
    <EmbeddedResource Remove="wwwroot\NewFolder\**" />
    <None Remove="TeyaUIModels\**" />
    <None Remove="TeyaUIViewModels\**" />
    <None Remove="wwwroot\NewFolder1\**" />
    <None Remove="wwwroot\NewFolder\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Components\Pages\Class.cs" />
    <Compile Remove="Components\Pages\Home1.razor.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Components\Pages\AmbientSolution.razor.razor" />
    <Content Remove="Components\Pages\Component.razor" />
    <Content Remove="Components\Pages\Component1.razor" />
    <Content Remove="Components\Pages\ContactForm.razor" />
    <Content Remove="Components\Pages\Home1.razor" />
    <Content Remove="Components\Pages\HomeCardComponent.razor" />
    <Content Remove="Components\Pages\HomeCardComponent.razor.razor" />
    <Content Remove="Components\Pages\Patient.razor.razor" />
    <Content Remove="Components\Pages\ProfileDialog.razor" />
    <Content Remove="Components\Pages\Weather.razor" />
    <Content Remove="Example.razor" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Components\Pages\As.razor.css" />
    <None Remove="Components\Pages\SignIn.razor.css" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
    <PackageReference Include="Azure.Communication.Email" Version="1.0.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Blazored.SessionStorage" Version="2.4.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.72" />
    <PackageReference Include="Markdig" Version="0.40.0" />
    <PackageReference Include="ModelContextProtocol" Version="0.1.0-preview.13" />
    <PackageReference Include="Moq" Version="4.20.72" />
	  <PackageReference Include="Syncfusion.Blazor.Layouts" Version="27.1.50" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.AzureAD.UI" Version="6.0.33" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.10" />
    <PackageReference Include="Microsoft.Azure.Search.Data" Version="10.1.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.7.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.10" />
    <PackageReference Include="Microsoft.Graph" Version="5.68.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.2.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.1.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="MudBlazor" Version="7.8.0" />
    <PackageReference Include="MudBlazor.FontIcons.MaterialIcons" Version="1.2.0" />
    <PackageReference Include="OpenAI" Version="2.1.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="Syncfusion.Blazor.Buttons" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Calendars" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Cards" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Charts" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Core" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Data" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.DropDowns" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Grid" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Inputs" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Lists" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Popups" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Schedule" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Themes" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.TreeGrid" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Grid.Windows" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Grid.WPF" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Licensing" Version="27.1.58" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Unity" Version="5.11.10" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.8" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.40.0" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.18.2" />
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="TeyaUIModels\TeyaUIModels.csproj" />
    <ProjectReference Include="TeyaUIViewModels\TeyaUIViewModels.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="TeyaAIScribeResource\TeyaAIScribeResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TeyaAIScribeResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="TeyaAIScribeResource\TeyaAIScribeResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TeyaAIScribeResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>


  <ItemGroup>
    <Compile Update="TeyaAIScribeResource\TeyaAIScribeResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TeyaAIScribeResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="TeyaAIScribeResource\TeyaAIScribeResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TeyaAIScribeResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>


 


	<ItemGroup>
		<Compile Update="Components\Pages\Appointments.razor.cs">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</Compile>
	</ItemGroup>


 


	<ItemGroup>
	  <Folder Include="Components\Shared\" />
	</ItemGroup>

</Project>
