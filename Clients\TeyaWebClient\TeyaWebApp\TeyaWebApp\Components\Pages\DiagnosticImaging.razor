﻿@page "/DiagnosticImaging"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject HttpClient Http
@using Syncfusion.Blazor.Inputs
@inject ISnackbar Snackbar
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using Syncfusion.Blazor.RichTextEditor
@inject IDiagnosticImagingService DiagnosticImagingService

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" aria-label="delete" @onclick="OpenDialog" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog Visible="isDialogOpen" Style="width: 85vw; max-width: 1000px;" OnBackdropClick="HandleBackdropClick">

    <TitleContent>
        <MudText Typo="Typo.h6" Color="Color.Primary">
            @Localizer["Diagnostic Imaging"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -2px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>

   <DialogContent>
                <MudGrid Spacing="3" Class="mb-3 mt-1">
                    <MudItem xs="6">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                            <MudText Typo="Typo.h6" Color="Color.Primary">@Localizer["Order Options"]</MudText>
                                </MudItem>
                                <MudItem xs="12">
                                    <MudText Typo="Typo.body1"> </MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.body1">@Localizer["DI Company"]</MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.body1">@Localizer["Type"]</MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.body1"></MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.DiCompany" /></MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.Type" /></MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.body1"></MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.body1">@Localizer["Lookup"]</MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.body1">@Localizer["Order name"]</MudText>
                                </MudItem>
                                <MudItem xs="4">
                                    <MudText Typo="Typo.body1">@Localizer["Starts with"]</MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.Lookup" /></MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.OrderName" /></MudText>
                                </MudItem>
                                <MudItem xs="4">
                            <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.StartsWith" /></MudText>
                                </MudItem>
                             </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="6">
                        <MudPaper Class="pa-3" Height="160px">
                            <MudGrid Spacing="2">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.body1">@Localizer["CC Results To"]</MudText>
                                </MudItem>
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6"><SfTextBox @bind-Value="newDiagnosticImaging.ccResults" /></MudText>
                                </MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>

                    <MudItem xs="6">
                <MudPaper Class="pa-2" Height="350px">
                    <MudGrid>
                            <MudItem xs="12">
                             <MudText Typo="Typo.h6" Color="Color.Primary">@Localizer["Lab Orders"]</MudText>
                            </MudItem>
                            <MudItem xs="12">
                                <MudText Typo="Typo.h6">
                            <SfGrid @ref="todayImagingGrid" TValue="DiagnosticImage" DataSource="@DiagnosticImagingList" AllowPaging="true">
                                <GridEditSettings AllowEditing="true" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings>
                                <GridPageSettings PageSize="5"></GridPageSettings>
                                <GridColumns>
                                    <GridColumn Field="RecordID" IsPrimaryKey="true" Visible="false"></GridColumn>
                                    <GridColumn Field="Type" HeaderText=@Localizer["Order Name"]></GridColumn>
                            <GridColumn Field="DiCompany" HeaderText=@Localizer["Lab Co."]></GridColumn>
                                </GridColumns>
                            </SfGrid>
                            </MudText>
                        </MudItem>
                    </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="6">
                        <MudPaper Class="pa-2" Height="350px">
                            <MudGrid>
                            <MudItem xs="12">
                             <MudText Typo="Typo.h6" Color="Color.Primary">@Localizer["Diagnostic Imaging History and Forecast"]</MudText>
                            </MudItem>
                            <MudItem xs="12">
                                <MudText Typo="Typo.h6">
                            <SfGrid @ref="futureImagingGrid" TValue="DiagnosticImage" DataSource="@DiagnosticImagingList" AllowPaging="true">
                                        <GridEditSettings AllowEditing="true" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings>
                                        <GridPageSettings PageSize="5"></GridPageSettings>
                                        <GridColumns>
                                            <GridColumn Field="RecordID" IsPrimaryKey="true" Visible="false"></GridColumn>
                                            <GridColumn Field="Type" HeaderText=@Localizer["Description"]></GridColumn>
                                            <GridColumn Field="CreatedDate" Format="MM/dd/yyyy" HeaderText=@Localizer["Date"]></GridColumn>
                                        </GridColumns>
                                    </SfGrid>
                                </MudText>
                            </MudItem>
                          </MudGrid>
                        </MudPaper>
                    </MudItem>
                    </MudGrid>
    </DialogContent>

        <DialogActions>
            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           OnClick="CancelChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </DialogActions>

</MudDialog>


    