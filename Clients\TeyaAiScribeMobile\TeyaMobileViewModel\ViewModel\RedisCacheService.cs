﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using TeyaMobileViewModel.TeyaUIViewModelResources;
namespace TeyaMobileViewModel.ViewModel
{
    public class RedisCacheService : ICacheService
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly IDatabase _cache;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly string _instanceName;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;

        public RedisCacheService(
            IConnectionMultiplexer redis,
            ILogger<RedisCacheService> logger,
            IConfiguration configuration,
            IStringLocalizer<TeyaUIViewModelsStrings> localizer)
        {
            _redis = redis;
            _cache = redis.GetDatabase();
            _logger = logger;
            _localizer = localizer;
            _instanceName = _localizer["TeyaWebApp:"];
        }

        private string GetFullKey(string key) => $"{_instanceName}{key}";

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getDataFunc, TimeSpan? expirationTime = null)
        {
            var fullKey = GetFullKey(key);

            try
            {
                var value = await _cache.StringGetAsync(fullKey);

                if (!value.IsNull)
                {
                    _logger.LogDebug(_localizer["CacheHit"], key);
                    return JsonSerializer.Deserialize<T>(value);
                }

                _logger.LogDebug(_localizer["CacheMiss"], key);
                var data = await getDataFunc();

                if (data != null)
                {
                    var serializedData = JsonSerializer.Serialize(data);
                    await _cache.StringSetAsync(
                        fullKey,
                        serializedData,
                        expirationTime ?? TimeSpan.FromMinutes(1));
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorCache"], key);
                return await getDataFunc();
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                var fullKey = GetFullKey(key);
                if (await _cache.KeyExistsAsync(fullKey))
                {
                    await _cache.KeyDeleteAsync(fullKey);
                    _logger.LogDebug($"Successfully removed key: {fullKey}");
                }
                else
                {
                    _logger.LogDebug($"Key not found for removal: {fullKey}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorRemovingCache"], key);
            }
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                var fullPattern = GetFullKey(pattern);
                var endpoints = _redis.GetEndPoints();
                var server = _redis.GetServer(endpoints.First());
                var keys = server.Keys(pattern: fullPattern + "*").ToList();

                foreach (var key in keys)
                {
                    await _cache.KeyDeleteAsync(key);
                }

                _logger.LogDebug(_localizer["RemovedCache"],
                    keys.Count, pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["RemoveError"], pattern);
            }
        }

        public async Task<bool> KeyExistsAsync(string key)
        {
            var fullKey = GetFullKey(key);
            return await _cache.KeyExistsAsync(fullKey);
        }
    }
}

