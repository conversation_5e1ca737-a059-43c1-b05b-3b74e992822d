﻿using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class MeasureService : IMeasureService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _measuresUrl;

        public MeasureService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _measuresUrl = Environment.GetEnvironmentVariable("AlertsAPIURL");
        }

        public async Task<List<Measure>> GetAllMeasuresAsync()
        {
            var apiUrl = $"{_measuresUrl}/api/QualityMeasure/GetMeasures";
            var response = await _httpClient.GetAsync(apiUrl);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Measure>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }

        public async Task AddMeasuresAsync(List<Measure> measures)
        {
            var apiUrl = $"{_measuresUrl}/api/QualityMeasure/AddMeasures";
            var content = new StringContent(
                System.Text.Json.JsonSerializer.Serialize(measures),
                Encoding.UTF8,
                "application/json");

            var response = await _httpClient.PostAsync(apiUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        public async Task UpdateMeasuresListAsync(List<Measure> measures)
        {
            var apiUrl = $"{_measuresUrl}/api/QualityMeasure/UpdateMeasures";
            var content = new StringContent(
                System.Text.Json.JsonSerializer.Serialize(measures),
                Encoding.UTF8,
                "application/json");

            var response = await _httpClient.PutAsync(apiUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        public async Task<string> AskGptAsync(string systemMessage, string userPrompt)
        {
            var apiUrl = $"{_measuresUrl}/api/QualityMeasure/ask-gpt?systemMessage={Uri.EscapeDataString(systemMessage)}&userPrompt={Uri.EscapeDataString(userPrompt)}";
            var response = await _httpClient.PostAsync(apiUrl, null);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsStringAsync();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }

        public async Task<List<string>> GenerateAlertsAsync(string patientInfo)
        {
            var apiUrl = $"{_measuresUrl}/api/QualityMeasure/GenerateAlerts";
            var content = new StringContent(
                $"\"{patientInfo}\"", // simple string, so manually JSON-wrap it in quotes
                Encoding.UTF8,
                "application/json");

            var response = await _httpClient.PostAsync(apiUrl, content);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<string>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }
    }
}
