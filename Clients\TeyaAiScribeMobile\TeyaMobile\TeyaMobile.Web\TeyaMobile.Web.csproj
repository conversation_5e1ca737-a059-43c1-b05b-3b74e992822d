<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.3.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.3.0" />

    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\TeyaMobileModel\TeyaMobileModel.csproj" />
    <ProjectReference Include="..\..\TeyaMobileViewModel\TeyaMobileViewModel.csproj" />
    <ProjectReference Include="..\TeyaMobile.Shared\TeyaMobile.Shared.csproj" />
  </ItemGroup>

</Project>