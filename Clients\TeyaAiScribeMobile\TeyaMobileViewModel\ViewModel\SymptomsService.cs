﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobileViewModel.ViewModel
{
    public class SymptomsService : ISymptomsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<SymptomsService> _localizer;
        private readonly ILogger<SymptomsService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public SymptomsService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<SymptomsService> localizer, ITokenService tokenService, ILogger<SymptomsService> logger)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
            _logger = logger;
            
        }
        /// <summary>
        /// Get all symptoms from  API through db
        /// </summary>
        /// <returns></returns>
        public async Task<List<Symptoms>> GetSymptomsAsync()
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Symptoms";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var symptoms = await response.Content.ReadFromJsonAsync<List<Symptoms>>();

                    if (symptoms == null || !symptoms.Any())
                    {
                        Console.WriteLine("[WARNING] API returned an empty or null symptom list.");
                        return new List<Symptoms>();
                    }

                    return symptoms;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[ERROR] Failed to retrieve symptoms. Status: {response.StatusCode}, Message: {errorContent}");
                    throw new HttpRequestException($"{_localizer["SymptomsRetrievalFailure"]} - {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] GetSymptomsAsync failed: {ex.Message}");
                return new List<Symptoms>(); 
            }
        }

        /// <summary>
        /// Adding symptoms to the database through API
        /// </summary>
        /// <param name="symptoms"></param>
        /// <returns></returns>
        public async Task<bool> AddSymptomsAsync(List<Symptoms> symptoms)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Symptoms";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = JsonContent.Create(symptoms)
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Symptoms successfully added. Status: {StatusCode}", response.StatusCode);
                    return true;
                }
                else
                {
                    var errorResponse = await response.Content.ReadAsStringAsync();
                    var errorMessage = $"{_localizer["SymptomAddFailure"]}: {response.StatusCode} - {errorResponse}";

                    _logger.LogError("Failed to add symptoms. Status: {StatusCode}, Response: {Response}", response.StatusCode, errorResponse);

                    return false; 
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception occurred while adding symptoms: {Message}", ex.Message);
                return false;
            }
        }

    }
}
