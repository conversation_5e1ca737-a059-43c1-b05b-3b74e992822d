﻿@page "/Procedure"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Inputs
@using TeyaWebApp.Model
@inject ISnackbar Snackbar
@using Syncfusion.Blazor.Grids
@inject IDialogService DialogService


@using Syncfusion.Blazor.RichTextEditor
@using MudBlazor
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@inject HttpClient Http

<SfRichTextEditor @ref="richTextEditor" Value="@richTextContent" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@GetToolbarItems()">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" @onclick="@(async () => await OpenBrowsePopup())" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="showBrowsePopup" Style="width: 85vw; max-width: 1200px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.2;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Procedure"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="4">
                        <MudAutocomplete T="CPT"
                                         Label="@Localizer["Search CPT By Codes or Description"]"
                                         Value="@selectedCPT"
                                         ValueChanged="OnCPTSelected"
                                         SearchFunc="SearchCPTCodes"
                                         ToStringFunc="@(cpt => cpt != null ? $"{cpt.CPTCode} - {cpt.Description}" : "")"
                                         CoerceText="true"
                                         Clearable="true"
                                         ResetValueOnEmptyText="true"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense"
                                         MinCharacters="2"
                                         Style="width: 100%;" />
                    </MudItem>

                    <MudItem xs="3" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewProcedure"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 70px; height: 35px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <SfGrid @ref="ProcedureGrid" TValue="Procedures" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@procedure" AllowPaging="true" PageSettings-PageSize="5" AllowEditing="true" GridLines="GridLine.Both">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionBegin="ActionBeginHandler" TValue="Procedures"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT"]" TextAlign="TextAlign.Center" Width="30" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Description" HeaderText="@Localizer["Description"]" TextAlign="TextAlign.Center" Width="85" AllowEditing="false"></GridColumn>
                        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" Width="50"></GridColumn>
                        <GridColumn Field="@nameof(Procedures.AssessmentData)"
                                    HeaderText="@Localizer["Related Assessment"]"
                                    TextAlign="TextAlign.Center"
                                    Width="75"
                                    EditTemplate="@AssessmentEditTemplate">
                        </GridColumn>
                        <GridColumn Field="@nameof(Procedures.ChiefComplaint)"
                                    HeaderText="@Localizer["Chief Complaint"]"
                                    TextAlign="TextAlign.Center"
                                    Width="60"
                                    EditTemplate="@ChiefComplaintEditTemplate">
                        </GridColumn>
                        <GridColumn Field="OrderedBy" HeaderText="@Localizer["Ordered By"]" TextAlign="TextAlign.Center" Width="50" AllowEditing="false"></GridColumn>
                        <GridColumn Field="OrderDate" HeaderText="@Localizer["Order Date"]" Width="40" TextAlign="TextAlign.Center" Format="MM/dd/yy"></GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="30">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               Dense="true"
                               OnClick="CancelChanges"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveChanges"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>

<style>
    ::deep .e-grid .e-headercell {
        border-right: 2px solid #c0c0c0 !important;
        border-bottom: 2px solid #c0c0c0 !important;
        padding: 8px !important;
        font-weight: bold;
    }

    ::deep .e-grid .e-rowcell {
        border-right: 2px solid #c0c0c0 !important;
        padding: 8px !important;
    }

    ::deep .e-grid .e-row {
        border-bottom: 2px solid #c0c0c0 !important;
    }

    ::deep .e-grid {
        border: 2px solid #c0c0c0 !important;
    }

        ::deep .e-grid .e-row:hover {
            background-color: #f5f5f5 !important;
        }
</style>