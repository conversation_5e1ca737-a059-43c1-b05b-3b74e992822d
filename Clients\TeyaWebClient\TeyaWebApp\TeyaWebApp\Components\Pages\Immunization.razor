﻿@page "/Immunization"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using System

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="Symbol">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                   @onclick="OpenNewDialogBox"
                                   Size="Size.Small" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_immunization" Style="width: 95vw; max-width: 1400px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["Immunization"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid>
                    <MudItem xs="8" Style="padding-right: 32px; border-right: 1px solid #E0E0E0;">
                        <MudText Typo="Typo.h6" Style="margin-bottom: 16px;">@Localizer["Immunization Records"]</MudText>

                        <SfGrid @ref="ImmunizationGrid" TValue="ImmunizationData" Style="font-size: 0.85rem; margin-top: 24px; width: 100%;"
                                DataSource="@immunization" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both">
                            <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                            <GridPageSettings PageSize="10"></GridPageSettings>
                            <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="ImmunizationData"></GridEvents>
                            <GridColumns>
                                <GridColumn Field="ImmunizationId" IsPrimaryKey="true" Visible="false"></GridColumn>
                                <GridColumn Field="GivenDate" HeaderText="@Localizer["Date"]" Width="100" Format="MM/dd/y" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                <GridColumn Field="Immunizations" HeaderText="@Localizer["Vaccine"]" Width="200" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                <GridColumn Field="CPTCode" HeaderText="@Localizer["CPT Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                <GridColumn Field="CVXCode" HeaderText="@Localizer["CVX Code"]" Width="100" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="false"></GridColumn>
                                <GridColumn Field="Comments" HeaderText="@Localizer["Comments"]" Width="180" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Center" AllowEditing="true"></GridColumn>
                                <GridColumn HeaderText="@Localizer["Actions"]" Width="80" TextAlign="TextAlign.Center">
                                    <GridCommandColumns>
                                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                                    </GridCommandColumns>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </MudItem>

                    <MudItem xs="4" Style="padding-left: 24px;">
						<MudText Typo="Typo.h6" Style="margin-bottom: 16px;">@Localizer["Search Immunization"]</MudText>
                        <MudSelect T="string" Label="@Localizer["Select Source"]" @bind-Value="selectedSource"
                                   Dense="true" Margin="Margin.Dense" Variant="Variant.Outlined">
                            <MudSelectItem T="string" Value="@("CDC")">@Localizer["CDC"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@("FDB")">@Localizer["FDB"]</MudSelectItem>
                        </MudSelect>
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 24px;">
                            @if (selectedSource == Source.CDC.ToString())
                            {
                                <MudAutocomplete T="string"
                                                 Label="@Localizer["Search Vaccines"]"
                                                 Value="VaccineName"
                                                 ValueChanged="OnICDNameChanged"
                                                 SearchFunc="SearchVaccinesData"
                                                 ToStringFunc="@(s => s)"
                                                 CoerceText="true"
                                                 Clearable="true"
                                                 Dense="true"
                                                 ResetValueOnEmptyText="true"
                                                 Variant="Variant.Outlined"
                                                 Margin="Margin.Dense"
                                                 MinCharacters="1"
                                                 Style="flex-grow: 1;" />
                            }
                            else if(selectedSource == Source.FDB.ToString()){
                                <MudAutocomplete T="FDBVaccines"
                                                 Label="@Localizer["Select Vaccine"]"
                                                 SearchFunc="SearchFDBVaccine"
                                                 ToStringFunc="@(vaccine => vaccine?.EVD_CVX_CD_DESC_SHORT)"
                                                 Clearable="true"
                                                 CoerceText="true"
                                                 MinCharacters="2"
                                                 ResetValueOnEmptyText="true"
                                                 ValueChanged="OnFDBVaccineSelected"
                                                 Value="FDBSelectedVaccine"
                                                 Dense="true"
                                                 Margin="Margin.Dense"
                                                 Variant="Variant.Outlined"
                                                 Style="flex-grow: 1;" />

                            }

                            <MudButton Color="Color.Primary"
                                       OnClick="AddNewSurgery"
                                       Variant="Variant.Filled"
                                       Style="height: 40px; min-width: 85px;">
                                @Localizer["Add"]
                            </MudButton>
                        </div>

                        <MudText Typo="Typo.h6" Style="margin-bottom: 16px; margin-top: 16px; border-top: 1px solid #f0f0f0; padding-top: 16px;">@Localizer["Vaccine Details"]</MudText>
                        <div style="display: flex; flex-direction: column; gap: 16px;">

                            <div style="display: flex; align-items: center; gap: 8px;">
                                <MudText Typo="Typo.body1" Style="min-width: 120px;">@Localizer["Vaccine Name"]</MudText>
                                <MudTextField @bind-Value="VaccineName" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                            </div>

                            <div style="display: flex; align-items: center; gap: 8px;">
                                <MudText Typo="Typo.body1" Style="min-width: 120px;">@Localizer["CPT Code"]</MudText>
                                <MudTextField @bind-Value="SelectedCPTCode" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                            </div>

                            <div style="display: flex; align-items: center; gap: 8px;">
                                <MudText Typo="Typo.body1" Style="min-width: 120px;">@Localizer["CVX Code"]</MudText>
                                <MudTextField @bind-Value="SelectedCVXCode" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                            </div>

                            <div style="display: flex; align-items: center; gap: 8px;">
                                <MudText Typo="Typo.body1" Style="min-width: 120px;">@Localizer["CPT Description"]</MudText>
                                <MudTextField @bind-Value="SelectedCPTDescription" ReadOnly="true" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                            </div>

                            <div style="display: flex; align-items: flex-start; gap: 8px;">
                                <MudText Typo="Typo.body1" Style="min-width: 120px; margin-top: 8px;">@Localizer["Comments"]</MudText>
                                <MudTextField @bind-Value="Comments" Lines="3" Variant="Variant.Outlined" Margin="Margin.Dense" Style="width: 100%;" />
                            </div>

                        </div>
                    </MudItem>
                </MudGrid>

                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0; margin-top: 24px;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               OnClick="CancelData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>




<style>
    ::deep .e-grid .e-headercell {
        border-right: 2px solid #c0c0c0 !important;
        border-bottom: 2px solid #c0c0c0 !important;
        padding: 8px !important;
        font-weight: bold;
    }

    ::deep .e-grid .e-rowcell {
        border-right: 2px solid #c0c0c0 !important;
        padding: 8px !important;
    }

    ::deep .e-grid .e-row {
        border-bottom: 2px solid #c0c0c0 !important;
    }

    ::deep .e-grid {
        border: 2px solid #c0c0c0 !important;
    }

        ::deep .e-grid .e-row:hover {
            background-color: #f5f5f5 !important;
        }
</style>