﻿namespace TeyaMobileModel.Model
{
    public class Appointment : IModel
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid ProviderId { get; set; }
        public Guid PatientId { get; set; }
        public Guid FacilityId { get; set; }
        public Guid? OrganisationId { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DateTime AppointmentDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public bool Subscription { get; set; }
        public string Facility { get; set; }
        public string PatientName { get; set; }
        public string Provider { get; set; }
        public string VisitType { get; set; }
        public string VisitStatus { get; set; }
        public string Reason { get; set; }
        public string Notes { get; set; }
        public string RoomNumber { get; set; }
        public Brush? Background { get; set; }
    }
}