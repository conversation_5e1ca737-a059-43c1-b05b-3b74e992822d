﻿using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public class UpToDateService : IUpToDateService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<UpToDateService> _logger;
        private readonly IStringLocalizer<UpToDateService> _localizer;
        private readonly ITokenService _tokenService;
        private readonly string _MemberService;
        private readonly string _accessToken;

        public UpToDateService(
            HttpClient httpClient,
            ILogger<UpToDateService> logger,
            IStringLocalizer<UpToDateService> localizer,
            ITokenService tokenService)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _tokenService = tokenService ?? throw new ArgumentNullException(nameof(tokenService));
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL") ?? throw new InvalidOperationException(_localizer["Base URL not configured"]);
            _accessToken = _tokenService.AccessToken ?? throw new InvalidOperationException(_localizer["Access token not available"]);
        }

        private HttpRequestMessage CreateRequestMessage(HttpMethod method, string url, HttpContent? content = null)
        {
            var request = new HttpRequestMessage(method, url);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);

            if (content != null)
            {
                request.Content = content;
            }

            return request;
        }

        public async Task<UpToDate?> GetUpToDateByIdAsync(Guid id)
        {
            UpToDate? result = null;
            try
            {
                var request = CreateRequestMessage(HttpMethod.Get, $"{_MemberService}/api/UpToDate/{id}");
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<UpToDate>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch UpToDate with ID {id}"], id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching UpToDate with ID {id}"], id);
                throw;
            }
            return result;
        }

        public async Task<bool> AddUpToDateAsync(UpToDate upToDate)
        {
            bool isSuccess = false;
            try
            {
                var content = JsonContent.Create(upToDate);
                var request = CreateRequestMessage(HttpMethod.Post, $"{_MemberService}/api/UpToDate", content);
                var response = await _httpClient.SendAsync(request);

                isSuccess = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error adding UpToDate"]);
                throw;
            }
            return isSuccess;
        }

        public async Task<bool> UpdateUpToDateAsync(Guid id, UpToDate upToDate)
        {
            bool isSuccess = false;
            try
            {
                var content = JsonContent.Create(upToDate);
                var request = CreateRequestMessage(HttpMethod.Put, $"{_MemberService}/api/UpToDate/{id}", content);
                var response = await _httpClient.SendAsync(request);

                isSuccess = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error updating UpToDate with ID {id}"], id);
                throw;
            }
            return isSuccess;
        }

        public async Task<bool> DeleteUpToDateAsync(Guid id)
        {
            bool isSuccess = false;
            try
            {
                var request = CreateRequestMessage(HttpMethod.Delete, $"{_MemberService}/api/UpToDate/{id}");
                var response = await _httpClient.SendAsync(request);

                isSuccess = response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error deleting UpToDate with ID {id}"], id);
                throw;
            }
            return isSuccess;
        }

        public async Task<List<UpToDate>?> GetAllUpToDatesAsync()
        {
            List<UpToDate>? result = null;
            try
            {
                var request = CreateRequestMessage(HttpMethod.Get, $"{_MemberService}/api/UpToDate");
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<UpToDate>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch all UpToDates"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching all UpToDates"]);
                throw;
            }
            return result;
        }

        public async Task<List<UpToDate>?> GetUpToDatesByNameAsync(string name)
        {
            List<UpToDate>? result = null;
            try
            {
                var request = CreateRequestMessage(HttpMethod.Get, $"{_MemberService}/api/UpToDate/search?name={name}");
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<UpToDate>>();
                }
                else
                {
                    _logger.LogWarning(_localizer["Failed to fetch UpToDates with name {name}"], name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching UpToDates with name {name}"], name);
                throw;
            }
            return result;
        }
    }
}
