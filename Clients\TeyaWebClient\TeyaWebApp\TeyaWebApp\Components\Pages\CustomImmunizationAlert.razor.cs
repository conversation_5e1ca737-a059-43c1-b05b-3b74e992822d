﻿using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Threading;

namespace TeyaWebApp.Components.Pages
{
    public partial class CustomImmunizationAlert
    {
        [Inject] public IVaccineService VaccineService { get; set; }
        [Inject] public IFDBService FDBService { get; set; }
        [Inject] public ICustomImmunizationAlertService CustomImmunizationAlertService { get; set; }
        [Inject] private ILogger<CustomImmunizationAlert> _logger { get; set; }
        [Inject] private IStringLocalizer<CustomImmunizationAlert> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }

        [CascadingParameter] private MudDialogInstance? MudDialog { get; set; }

        private SfGrid<CustomImmunizationAlerts>? CustomImmunizationAlertGrid;

        private List<CustomImmunizationAlerts> customImmunizationAlerts = new();
        private List<CustomImmunizationAlerts> deleteAlertList = new();
        private List<CustomImmunizationAlerts> addList = new();
        public enum Source { CDC, FDB }

        private string selectedSource = Source.CDC.ToString();

        private string alertName = string.Empty;
        private string alertDescription = string.Empty;
        private string webReference = string.Empty;
        private int? ageLowerBound;
        private int? ageUpperBound;
        private string orderSet = string.Empty;
        private string gender = "Both";

        // New field for source selection
        private string selectedVaccineName = string.Empty;
        private FDBVaccines? FDBSelectedVaccine;

        [Inject]
        private PatientService PatientService { get; set; } = default!;

        public string VaccineName { get; set; }
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }
        private List<Vaccines> _details { get; set; } = new List<Vaccines>();
        private List<FDBVaccines> _fdbVaccines { get; set; } = new List<FDBVaccines>();

        protected override async Task OnInitializedAsync()
        {
            _details = await VaccineService.GetAllVaccinesDataAsync();
            _fdbVaccines = await FDBService.GetVaccines();

            PatientId = PatientService.PatientData.Id;
            OrgID = PatientService.PatientData.OrganizationID;
            try
            {
                await LoadAlertsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving immunization alert data");
            }
        }

        private async Task LoadAlertsAsync()
        {
            var existingAlerts = await CustomImmunizationAlertService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, false);
            customImmunizationAlerts = existingAlerts?.ToList() ?? new List<CustomImmunizationAlerts>();

            int emptyRowsNeeded = 9 - customImmunizationAlerts.Count;
            if (emptyRowsNeeded > 0)
            {
                customImmunizationAlerts.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                    .Select(_ => new CustomImmunizationAlerts
                    {
                        Name = string.Empty,
                        Description = string.Empty,
                        WebReference = string.Empty,
                        OrderSet = string.Empty,
                        Gender = string.Empty
                    }));
            }
        }

        public void ActionCompletedHandler(ActionEventArgs<CustomImmunizationAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedAlert = args.Data;
                var existingItem = addList.FirstOrDefault(v => v.Id == deletedAlert.Id);

                if (existingItem != null)
                {
                    addList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteAlertList.Add(args.Data);
                }
            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<CustomImmunizationAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    _localizer["Confirm Delete"],
                    _localizer["Do you want to delete this entry?"],
                    yesText: _localizer["Yes"],
                    noText: _localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        protected async Task<IEnumerable<string>> SearchVaccinesData(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(value))
                return new List<string>();

            var searchResults = _details
                .Where(t => !string.IsNullOrEmpty(t.VaccineName) && t.VaccineName.Contains(value, StringComparison.OrdinalIgnoreCase))
                .Select(t => t.VaccineName)
                .Distinct()
                .ToList();

            cancellationToken.ThrowIfCancellationRequested();

            return searchResults;
        }

        protected async Task<IEnumerable<FDBVaccines>> SearchFDBVaccine(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(value) || value.Length < 2)
                return new List<FDBVaccines>();

            var searchResults = _fdbVaccines
                .Where(t => !string.IsNullOrEmpty(t.EVD_CVX_CD_DESC_SHORT) &&
                           t.EVD_CVX_CD_DESC_SHORT.Contains(value, StringComparison.OrdinalIgnoreCase))
                .ToList();

            cancellationToken.ThrowIfCancellationRequested();

            return searchResults;
        }

        private void OnFDBVaccineSelected(FDBVaccines vaccine)
        {
            if (vaccine != null)
            {
                FDBSelectedVaccine = vaccine;
                alertName = vaccine.EVD_CVX_CD_DESC_SHORT; // Set alertName when vaccine is selected
            }
        }

        private async Task SaveData()
        {
            try
            {
                if (addList.Count > 0)
                {
                    foreach (var alert in addList)
                    {
                        alert.IsActive = true;
                    }
                    await CustomImmunizationAlertService.AddCustomImmunizationAlertsAsync(addList, OrgID, false);
                }

                if (deleteAlertList.Count > 0)
                {
                    await CustomImmunizationAlertService.UpdateCustomImmunizationAlertsListAsync(deleteAlertList, OrgID, false);
                }

                var existingAlerts = customImmunizationAlerts.Where(a => !string.IsNullOrEmpty(a.Name) && a.Id != Guid.Empty).ToList();
                if (existingAlerts.Count > 0)
                {
                    await CustomImmunizationAlertService.UpdateCustomImmunizationAlertsListAsync(existingAlerts, OrgID, false);
                }

                deleteAlertList.Clear();
                addList.Clear();

                await LoadAlertsAsync();
                ResetInputFields();

                Snackbar.Add(_localizer["Immunization alerts saved successfully"], Severity.Success);

                MudDialog?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving immunization alert data");
                Snackbar.Add(_localizer["Failed to save immunization alert records"], Severity.Error);
            }
        }

        private async Task CancelData()
        {
            deleteAlertList.Clear();
            addList.Clear();
            await LoadAlertsAsync();
            ResetInputFields();

            MudDialog?.Close();
        }

        private void ResetInputFields()
        {
            alertName = string.Empty;
            alertDescription = string.Empty;
            webReference = string.Empty;
            ageLowerBound = null;
            ageUpperBound = null;
            orderSet = string.Empty;
            gender = "Both";
            selectedVaccineName = string.Empty;
            FDBSelectedVaccine = null;
        }

        private async Task AddNewAlert()
        {
            try
            {
                // Parse the string to Source enum
                if (!Enum.TryParse<Source>(selectedSource, out Source sourceType))
                {
                    Snackbar.Add(_localizer["Invalid source type"], Severity.Warning);
                    return;
                }

                // Set alertName based on selected source
                switch (sourceType)
                {
                    case Source.CDC:
                        if (string.IsNullOrEmpty(selectedVaccineName))
                        {
                            Snackbar.Add(_localizer["Please select a vaccine"], Severity.Warning);
                            return;
                        }
                        alertName = selectedVaccineName;
                        break;

                    case Source.FDB:
                        if (FDBSelectedVaccine == null)
                        {
                            Snackbar.Add(_localizer["Please select a vaccine"], Severity.Warning);
                            return;
                        }
                        alertName = FDBSelectedVaccine.EVD_CVX_CD_DESC_SHORT;
                        break;
                }

                if (string.IsNullOrEmpty(alertName))
                {
                    Snackbar.Add(_localizer["Please select a vaccine"], Severity.Warning);
                    return;
                }

                var emptyRow = customImmunizationAlerts.FirstOrDefault(i => string.IsNullOrEmpty(i.Name));

                if (emptyRow == null)
                {
                    emptyRow = new CustomImmunizationAlerts();
                    customImmunizationAlerts.Add(emptyRow);
                }

                emptyRow.Id = Guid.NewGuid();
                emptyRow.PatientId = PatientId;
                emptyRow.pcpId = Guid.Parse(User.id);
                emptyRow.OrganizationId = OrgID ?? Guid.Empty;
                emptyRow.CreatedDate = DateTime.Now;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.Name = alertName;
                emptyRow.Description = alertDescription;
                emptyRow.WebReference = webReference;
                emptyRow.AgeLowerBound = ageLowerBound;
                emptyRow.AgeUpperBound = ageUpperBound;
                emptyRow.OrderSet = orderSet;
                emptyRow.Gender = gender;
                emptyRow.IsActive = true;

                addList.Add(emptyRow);

                if (CustomImmunizationAlertGrid != null)
                {
                    await CustomImmunizationAlertGrid.Refresh();
                }

                ResetInputFields();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new immunization alert");
                Snackbar.Add(_localizer["Failed to add immunization alert"], Severity.Error);
            }
        }
    }
}