﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.TeyaUIViewModelResources;
using static System.Net.WebRequestMethods;

namespace TeyaMobileViewModel.ViewModel
{
    public class ReviewOfSystemService : IReviewOfSystemService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotesUrl;
        private readonly ITokenService _tokenService;

        public ReviewOfSystemService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotesUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<ReviewOfSystem>> GetAllByIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotesUrl}/api/ReviewOfSystem/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<ReviewOfSystem>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<ReviewOfSystem>> GetAllByIdAndIsActiveAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotesUrl}/api/ReviewOfSystem/{id}/IsActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<ReviewOfSystem>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task AddReviewOfSystemAsync(List<ReviewOfSystem> reviewOfSystems)
        {
            var apiUrl = $"{_EncounterNotesUrl}/api/ReviewOfSystem/AddReviewOfSystem";

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(reviewOfSystems);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task UpdateReviewOfSystemAsync(ReviewOfSystem reviewOfSystem)
        {
            var apiUrl = $"{_EncounterNotesUrl}/api/ReviewOfSystem/{reviewOfSystem.PatientId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(reviewOfSystem);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdateReviewOfSystemListAsync(List<ReviewOfSystem> reviewOfSystems)
        {
            var apiUrl = $"{_EncounterNotesUrl}/api/ReviewOfSystem/UpdateReviewOfSystemList";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(reviewOfSystems);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task DeleteReviewOfSystemByEntityAsync(ReviewOfSystem reviewOfSystem)
        {
            if (reviewOfSystem == null)
            {
                throw new ArgumentNullException(nameof(reviewOfSystem), _localizer["InvalidRecord"]);
            }

            var apiUrl = $"{_EncounterNotesUrl}/api/ReviewOfSystem/DeleteReviewOfSystem";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(reviewOfSystem);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DeleteLogError"]);
            }
        }
    }
}
