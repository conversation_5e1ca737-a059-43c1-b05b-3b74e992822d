﻿@page "/appointments"
@using TeyaMobileModel.Model
@using TeyaMobileViewModel.ViewModel
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Popups
@inject IAppointmentService AppointmentService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<PageTitle>Today's Schedule</PageTitle>

<div class="doctor-scheduler">
    <!-- Compact Header with Date and Stats -->
    <div class="scheduler-header">
        <div class="date-section" @onclick="OpenDatePicker">
            <div class="date-info">
                <span class="current-date">@selectedDate.ToString("MMM dd")</span>
                <span class="day-name">@selectedDate.ToString("ddd")</span>
            </div>
            <i class="e-icons e-calendar date-icon"></i>
        </div>

        <!-- Compact Stats -->
        <div class="stats-container">
            <div class="stat-chip total">
                <span class="stat-count">@totalAppointments</span>
                <span class="stat-label">Total</span>
            </div>
            <div class="stat-chip completed">
                <span class="stat-count">@completedAppointments</span>
                <span class="stat-label">Done</span>
            </div>
            <div class="stat-chip pending">
                <span class="stat-count">@pendingAppointments</span>
                <span class="stat-label">Pending</span>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <span>Loading appointments...</span>
        </div>
    }

    <!-- Main Scheduler - Day View -->
    <div class="scheduler-container">
        <Syncfusion.Blazor.Schedule.SfSchedule TValue="AppointmentData"
                                               @ref="ScheduleRef"
                                               SelectedDate="@selectedDate"
                                               CurrentView="View.Day"
                                               Height="@schedulerHeight"
                                               StartHour="08:00"
                                               EndHour="18:00"
                                               ShowQuickInfo="false"
                                               ShowHeaderBar="false"
                                               AllowDragAndDrop="false"
                                               AllowResizing="false">
            <ScheduleViews>
                <ScheduleView Option="View.Day">
                    <ScheduleViewTimeScale Enable="true" Interval="30" SlotCount="2"></ScheduleViewTimeScale>
                </ScheduleView>
            </ScheduleViews>
            <ScheduleEventSettings DataSource="@appointments"
                                   AllowAdding="false"
                                   AllowDeleting="false"
                                   AllowEditing="false">
                <Template>
                    @{
                        var appointment = context as AppointmentData;
                    }
                    <div class="appointment-card @GetAppointmentStatusClass(appointment)"
                         @onclick="() => OnAppointmentCardClick(appointment)">
                        <div class="appointment-header">
                            <span class="patient-name">@appointment.PatientName</span>
                            <div class="time-status">
                                <span class="appointment-time">
                                    @appointment.StartTime.ToString("HH:mm")
                                </span>
                                <span class="status-dot @appointment.VisitStatus.ToLower().Replace(" ", "-")"></span>
                            </div>
                        </div>
                        <div class="appointment-details">
                            <div class="detail-row">
                                <span class="visit-type">@appointment.VisitType</span>
                                @if (!string.IsNullOrEmpty(appointment.RoomNumber))
                                {
                                    <span class="room-number">Room @appointment.RoomNumber</span>
                                }
                            </div>
                            @if (!string.IsNullOrEmpty(appointment.Reason))
                            {
                                <div class="reason">@appointment.Reason</div>
                            }
                        </div>
                    </div>
                </Template>
            </ScheduleEventSettings>
            <ScheduleEvents TValue="AppointmentData"
                            OnEventClick="OnAppointmentClick">
            </ScheduleEvents>
        </Syncfusion.Blazor.Schedule.SfSchedule>
    </div>

    <!-- Error Message -->
    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="error-container">
            <div class="error-message">
                <i class="e-icons e-error"></i>
                <span>@errorMessage</span>
                <button class="retry-btn" @onclick="RetryLoadAppointments">Retry</button>
            </div>
        </div>
    }
</div>

<!-- Date Picker Popup -->
<Syncfusion.Blazor.Popups.SfDialog @ref="DatePickerDialog"
                                   Width="320px"
                                   Height="auto"
                                   IsModal="true"
                                   ShowCloseIcon="true"
                                   Visible="@isDatePickerVisible"
                                   VisibleChanged="@OnDatePickerVisibilityChanged"
                                   CssClass="date-picker-dialog">
    <DialogTemplates>
        <Header>
            <div class="dialog-header">Select Date</div>
        </Header>
        <Content>
            <div class="calendar-popup">
                <Syncfusion.Blazor.Calendars.SfCalendar TValue="DateTime"
                                                        Value="@selectedDate"
                                                        ValueChanged="@OnDateSelected"
                                                        CssClass="popup-calendar">
                </Syncfusion.Blazor.Calendars.SfCalendar>
            </div>
        </Content>
    </DialogTemplates>
</Syncfusion.Blazor.Popups.SfDialog>

<style>
    .doctor-scheduler {
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: #f8f9fa;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .scheduler-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 12px 16px;
        color: white;
        display: flex;
        align-items: center;
        gap: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        z-index: 100;
        min-height: 60px;
    }

    .date-section {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 8px;
        transition: background-color 0.2s;
        min-width: 80px;
        border: 1px solid rgba(255,255,255,0.2);
    }

        .date-section:hover {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.4);
        }

    .date-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .current-date {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.2;
    }

    .day-name {
        font-size: 11px;
        opacity: 0.8;
        line-height: 1;
    }

    .date-icon {
        font-size: 16px;
        opacity: 0.8;
    }

    .stats-container {
        display: flex;
        gap: 6px;
        flex: 1;
        justify-content: center;
    }

    .stat-chip {
        background: rgba(255,255,255,0.15);
        border-radius: 12px;
        padding: 4px 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 45px;
        backdrop-filter: blur(10px);
    }

        .stat-chip.total {
            background: rgba(33, 150, 243, 0.2);
        }

        .stat-chip.completed {
            background: rgba(76, 175, 80, 0.2);
        }

        .stat-chip.pending {
            background: rgba(255, 152, 0, 0.2);
        }

    .stat-count {
        font-size: 16px;
        font-weight: 700;
        line-height: 1;
    }

    .stat-label {
        font-size: 9px;
        opacity: 0.9;
        text-transform: uppercase;
        font-weight: 500;
        line-height: 1;
        margin-top: 2px;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        background: #fff;
        margin: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    @@keyframes spin {
        0%

    {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }

    }

    .error-container {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        z-index: 1000;
    }

    .error-message {
        background: #ffebee;
        color: #c62828;
        padding: 12px 16px;
        border-radius: 8px;
        border-left: 4px solid #f44336;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .retry-btn {
        background: #f44336;
        color: white;
        border: none;
        padding: 4px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        margin-left: auto;
    }

    .scheduler-container {
        flex: 1;
        overflow: auto;
        background: #fff;
        max-height: calc(100vh - 80px);
    }

    .appointment-card {
        padding: 8px 10px;
        border-radius: 8px;
        margin: 2px;
        border-left: 3px solid #667eea;
        background: linear-gradient(135deg, #f8f9ff 0%, #e8edff 100%);
        box-shadow: 0 1px 4px rgba(102, 126, 234, 0.1);
        cursor: pointer;
        transition: all 0.2s ease;
        overflow: hidden;
    }

        .appointment-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(102, 126, 234, 0.2);
        }

        .appointment-card.completed {
            border-left-color: #4caf50;
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
        }

        .appointment-card.cancelled {
            border-left-color: #f44336;
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
        }

        .appointment-card.confirmed {
            border-left-color: #2196f3;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .appointment-card.pending {
            border-left-color: #ff9800;
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        }

    .appointment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
    }

    .patient-name {
        font-weight: 600;
        font-size: 14px;
        color: #333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .time-status {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .appointment-time {
        font-size: 11px;
        font-weight: 500;
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
    }

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #667eea;
    }

        .status-dot.completed {
            background: #4caf50;
        }

        .status-dot.cancelled {
            background: #f44336;
        }

        .status-dot.confirmed {
            background: #2196f3;
        }

        .status-dot.pending {
            background: #ff9800;
        }

    .appointment-details {
        font-size: 11px;
        color: #666;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2px;
    }

    .visit-type {
        font-weight: 500;
        color: #333;
    }

    .room-number {
        font-size: 10px;
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        padding: 1px 4px;
        border-radius: 3px;
    }

    .reason {
        font-size: 10px;
        color: #888;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Date Picker Dialog */
    .date-picker-dialog .e-dialog {
        border-radius: 12px !important;
    }

    .dialog-header {
        font-weight: 600;
        color: #333;
    }

    .calendar-popup {
        padding: 8px;
    }

    .popup-calendar {
        border: none !important;
    }

    /* Mobile optimizations */
    @@media (max-width: 768px) {
        .scheduler-header

    {
        padding: 8px 12px;
        flex-wrap: wrap;
        min-height: 70px;
    }

    .stats-container {
        order: -1;
        width: 100%;
        justify-content: space-around;
        margin-bottom: 8px;
    }

    .stat-chip {
        min-width: 50px;
        padding: 6px 10px;
    }

    .stat-count {
        font-size: 18px;
    }

    .stat-label {
        font-size: 10px;
    }

    .current-date {
        font-size: 14px;
    }

    .appointment-card {
        margin: 1px;
        padding: 6px 8px;
    }

    .patient-name {
        font-size: 13px;
    }

    .appointment-time {
        font-size: 10px;
    }

    .scheduler-container {
        max-height: calc(100vh - 90px);
    }

    }

    /* Syncfusion overrides for Day view */
    .e-schedule .e-day-view .e-time-cells-wrap {
        width: 60px !important;
    }

    .e-schedule .e-content-wrap {
        overflow-y: auto !important;
        max-height: calc(100vh - 140px) !important;
    }

    .e-schedule .e-day-view .e-appointment {
        padding: 0 !important;
        margin: 2px !important;
    }
</style>

