﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaMobileModel.Model;

namespace TeyaMobileViewModel.ViewModel
{
    public interface IAllergyService
    {
        Task<List<Allergy>> GetAllergyByIdAsync(Guid id);
        Task<List<Allergy>> GetAllergyByIdAsyncAndIsActive(Guid id);
        Task AddAllergyAsync(List<Allergy> Allergy);
        Task DeleteAllergyAsync(Allergy allergy);
        Task UpdateAllergyAsync(Allergy Allergy);
        Task UpdateAllergyListAsync(List<Allergy> newAllergies, List<Allergy> updatedAllergies, List<Allergy> deletedAllergies, Guid patientId);
    }
}