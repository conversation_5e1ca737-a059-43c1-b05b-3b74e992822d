using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IUserThemeService
    {
        Task<IEnumerable<UserTheme>> GetUserThemesAsync();
        Task<UserTheme> GetUserThemeByIdAsync(Guid id);
        Task AddUserThemeAsync(UserTheme userTheme);
        Task UpdateUserThemeAsync(UserTheme userTheme);
        Task DeleteUserThemeByIdAsync(Guid id);
    }
}
