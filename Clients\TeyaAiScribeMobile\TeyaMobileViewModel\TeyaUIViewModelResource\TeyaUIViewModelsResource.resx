﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="GetLogError" xml:space="preserve">
    <value>An error occurred while fetching members.</value>
  </data>
  <data name="PostLogError" xml:space="preserve">
    <value>An error occurred while registering members.</value>
  </data>
  <data name="NoMembers" xml:space="preserve">
    <value>No members to register.</value>
  </data>
  <data name="SuccessfulRegistration" xml:space="preserve">
    <value>Members registered successfully.</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>A database error occurred.</value>
  </data>
  <data name="DeleteSuccessful" xml:space="preserve">
    <value>Delete Successful</value>
  </data>
  <data name="MemberNotFound" xml:space="preserve">
    <value>Member Not Found</value>
  </data>
  <data name="UpdateLogError" xml:space="preserve">
    <value>Log not updated</value>
  </data>
  <data name="UpdateSuccessful" xml:space="preserve">
    <value>Update Successful</value>
  </data>
  <data name="InvalidId" xml:space="preserve">
    <value>The given Id is not valid</value>
  </data>
  <data name="InvalidMember" xml:space="preserve">
    <value>Not a valid member</value>
  </data>
  <data name="DeleteLogError" xml:space="preserve">
    <value>Item is not deleted</value>
  </data>
  <data name="application/json" xml:space="preserve">
    <value>application/json</value>
  </data>
  <data name="AccessUpdateSuccessful" xml:space="preserve">
    <value>Access updated successfully</value>
  </data>
  <data name="AccessNotFound" xml:space="preserve">
    <value>Access record not found</value>
  </data>
  <data name="RegistrationFailed" xml:space="preserve">
    <value>Registration Failed</value>
  </data>
  <data name="HTTPRequestError" xml:space="preserve">
    <value>HTTP Request Error:</value>
  </data>
  <data name="MemberServiceApi_base_url" xml:space="preserve">
    <value>http://localhost/MemberServiceApi/api/Products</value>
  </data>
  <data name="ProductRetrievalFailure" xml:space="preserve">
    <value>Failed to retrieve products
</value>
  </data>
  <data name="SuccessfulRequest" xml:space="preserve">
    <value>Request successful</value>
  </data>
  <data name="ApiSettings:RegistrationUrl" xml:space="preserve">
    <value>ApiSettings:RegistrationUrl</value>
  </data>
  <data name="ApiSettings:MembersUrl" xml:space="preserve">
    <value>ApiSettings:MembersUrl</value>
  </data>
  <data name="{productId}" xml:space="preserve">
    <value>{productId}</value>
  </data>
  <data name="ApiSettings:ProductsUrl" xml:space="preserve">
    <value>ApiSettings:ProductsUrl</value>
  </data>
  <data name="ApiSettings:UpdateAccessUrl" xml:space="preserve">
    <value>ApiSettings:UpdateAccessUrl</value>
  </data>
  <data name="productId" xml:space="preserve">
    <value>productId</value>
  </data>
  <data name="LicenseRetrievalFailure" xml:space="preserve">
    <value>License Retrieval Failure</value>
  </data>
  <data name="ErrorFetchingProducts" xml:space="preserve">
    <value>Error fetching products</value>
  </data>
  <data name="ErrorFetchingMembersForProduct" xml:space="preserve">
    <value>Error Fetching Members For Product</value>
  </data>
  <data name="ErrorSavingMemberAccessUpdates" xml:space="preserve">
    <value>Error Saving Member Access Updates</value>
  </data>
  <data name="ErrorDuringRegistration" xml:space="preserve">
    <value>Error During Registration</value>
  </data>
  <data name="ErrorFetchingLicenses" xml:space="preserve">
    <value>Error Fetching Licenses</value>
  </data>
  <data name="ErrorSavingLicenses" xml:space="preserve">
    <value>Error Saving Licenses</value>
  </data>
  <data name="API Response: {ResponseData}" xml:space="preserve">
    <value> API Response: {ResponseData}</value>
  </data>
  <data name="Error deserializing response: {0}" xml:space="preserve">
    <value>Error deserializing response: {0}</value>
  </data>
  <data name="MemberRegistrationError" xml:space="preserve">
    <value>An error occurred during member registration.</value>
  </data>
  <data name="JsonDeserializationError" xml:space="preserve">
    <value>An error occurred while deserializing the response: {0}</value>
  </data>
  <data name="UsernameRequired" xml:space="preserve">
    <value>UsernameRequired</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>PasswordRequired</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>InvalidEmailFormat</value>
  </data>
  <data name="EmailRequired" xml:space="preserve">
    <value>EmailRequired</value>
  </data>
  <data name="UserAlreadyExists" xml:space="preserve">
    <value>UserAlreadyExists</value>
  </data>
  <data name="ErrorParsingUserDetails" xml:space="preserve">
    <value>ErrorParsingUserDetails</value>
  </data>
  <data name="ErrorFetchingFullUserDetails" xml:space="preserve">
    <value>ErrorFetchingFullUserDetails</value>
  </data>
  <data name="MemberRegisteredSuccessfully" xml:space="preserve">
    <value>MemberRegisteredSuccessfully</value>
  </data>
  <data name="ErrorRegisteringMember" xml:space="preserve">
    <value>ErrorRegisteringMember</value>
  </data>
  <data name="Error parsing user details" xml:space="preserve">
    <value>Error parsing user details</value>
  </data>
  <data name="Error registering member" xml:space="preserve">
    <value>Error registering member</value>
  </data>
  <data name="Member registered successfully!" xml:space="preserve">
    <value>Member registered successfully!</value>
  </data>
  <data name="Error fetching full user details" xml:space="preserve">
    <value>Error fetching full user details</value>
  </data>
  <data name="AccessTokenMissing" xml:space="preserve">
    <value>AccessTokenMissing</value>
  </data>
  <data name="FailedToGetUserDetails" xml:space="preserve">
    <value>FailedToGetUserDetails</value>
  </data>
  <data name="ErrorGettingUserDetails" xml:space="preserve">
    <value>ErrorGettingUserDetails</value>
  </data>
  <data name="ErrorGettingFullUserDetails" xml:space="preserve">
    <value>ErrorGettingFullUserDetails</value>
  </data>
  <data name="FailedToGetFullUserDetails" xml:space="preserve">
    <value>FailedToGetFullUserDetails</value>
  </data>
  <data name="UserProfileUpdated" xml:space="preserve">
    <value>User profile updated successfully.</value>
  </data>
  <data name="UpdateFailed" xml:space="preserve">
    <value>Failed to update user profile.</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>Error updating profile.</value>
  </data>
  <data name="AccountDeleted" xml:space="preserve">
    <value>Account deleted successfully.</value>
  </data>
  <data name="DeleteError" xml:space="preserve">
    <value>Error deleting account.</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome to Teya Web App!</value>
  </data>
  <data name="Saved" xml:space="preserve">
    <value>User details saved successfully.</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Failed to save user data.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error occurred while processing.</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="Guest" xml:space="preserve">
    <value>Guest</value>
  </data>
  <data name="ErrorFetchingUsers" xml:space="preserve">
    <value>ErrorFetchingUsers</value>
  </data>
  <data name="UserCreated" xml:space="preserve">
    <value>UserCreated</value>
  </data>
  <data name="UserCreationFailed" xml:space="preserve">
    <value>UserCreationFailed</value>
  </data>
  <data name="ErrorCreatingUser" xml:space="preserve">
    <value>ErrorCreatingUser</value>
  </data>
  <data name="ErrorDeletingUsers" xml:space="preserve">
    <value>ErrorDeletingUsers</value>
  </data>
  <data name="SelectSingleUser" xml:space="preserve">
    <value>SelectSingleUser</value>
  </data>
  <data name="MemberDeletionFailed" xml:space="preserve">
    <value>MemberDeletionFailed</value>
  </data>
  <data name="DeleteFailedStatusCode" xml:space="preserve">
    <value>DeleteFailedStatusCode</value>
  </data>
  <data name="MemberDeletedSuccessfully" xml:space="preserve">
    <value>MemberDeletedSuccessfully</value>
  </data>
  <data name="ApiSettings:DeleteRegistrationMemberUrl" xml:space="preserve">
    <value>ApiSettings:DeleteRegistrationMemberUrl</value>
  </data>
  <data name="ErrorInvitingUser" xml:space="preserve">
    <value>ErrorInvitingUser</value>
  </data>
  <data name="FailedToSendInvitation" xml:space="preserve">
    <value>FailedToSendInvitation</value>
  </data>
  <data name="UserInvitedSuccessfully" xml:space="preserve">
    <value>UserInvitedSuccessfully</value>
  </data>
  <data name="UserCreationError" xml:space="preserve">
    <value>UserCreationError</value>
  </data>
  <data name="UserCreatedSuccess" xml:space="preserve">
    <value>UserCreatedSuccess</value>
  </data>
  <data name="InviteRedirectUrlDev" xml:space="preserve">
    <value>https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io</value>
    <comment>developer mode</comment>
  </data>
  <data name="InviteRedirectUrl" xml:space="preserve">
    <value>https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io</value>
  </data>
  <data name="ErrorDuringMemberSubmissionUpdate" xml:space="preserve">
    <value>ErrorDuringMemberSubmissionUpdate</value>
  </data>
  <data name="wel" xml:space="preserve">
    <value>welcome to teya</value>
    <comment>welcome to teya</comment>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>mail</value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>surname</value>
  </data>
  <data name="GivenName" xml:space="preserve">
    <value>givenName</value>
  </data>
  <data name="Display_Name" xml:space="preserve">
    <value>displayName</value>
  </data>
  <data name="StreetAddress" xml:space="preserve">
    <value>streetAddress</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>PostalCode</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>state</value>
  </data>
  <data name="ExtensionCompanyName" xml:space="preserve">
    <value>ExtensionCompanyName</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>country</value>
  </data>
  <data name="UserType" xml:space="preserve">
    <value>userType</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>jobTitle</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>companyName</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>department</value>
  </data>
  <data name="OfficeLocation" xml:space="preserve">
    <value>OfficeLocation</value>
  </data>
  <data name="MobilePhone" xml:space="preserve">
    <value>MobilePhone</value>
  </data>
  <data name="Id" xml:space="preserve">
    <value>id</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>city</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>CreateUser</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="UserPrincipalName" xml:space="preserve">
    <value>UserPrincipalName</value>
  </data>
  <data name="MailNickname" xml:space="preserve">
    <value>MailNickname</value>
  </data>
  <data name="DisplayName" xml:space="preserve">
    <value>DisplayName</value>
  </data>
  <data name="UserManagementRoute" xml:space="preserve">
    <value>/usermanagement</value>
  </data>
  <data name="InviteUrl" xml:space="preserve">
    <value>https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io</value>
  </data>
  <data name="InviteUser" xml:space="preserve">
    <value>InviteUser</value>
  </data>
  <data name="UserEmailAddress" xml:space="preserve">
    <value>UserEmailAddress</value>
  </data>
  <data name="EnterEmailAddress" xml:space="preserve">
    <value>EnterEmailAddress</value>
  </data>
  <data name="EnterDisplayName" xml:space="preserve">
    <value>EnterDisplayName</value>
  </data>
  <data name="RedirectUrl" xml:space="preserve">
    <value>RedirectUrl</value>
  </data>
  <data name="EnterRedirectUrl" xml:space="preserve">
    <value>EnterRedirectUrl</value>
  </data>
  <data name="CustomMessage" xml:space="preserve">
    <value>CustomMessage</value>
  </data>
  <data name="EnterCustomMessage" xml:space="preserve">
    <value>EnterCustomMessage</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="DeleteAccount" xml:space="preserve">
    <value>DeleteAccount</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>SaveChanges</value>
  </data>
  <data name="Given Name" xml:space="preserve">
    <value>GivenName</value>
  </data>
  <data name="Patients" xml:space="preserve">
    <value>Patients</value>
  </data>
  <data name="Who" xml:space="preserve">
    <value>Who</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Choices" xml:space="preserve">
    <value>Choices</value>
  </data>
  <data name="Employer" xml:space="preserve">
    <value>Employer</value>
  </data>
  <data name="Stats" xml:space="preserve">
    <value>Stats</value>
  </data>
  <data name="Misc" xml:space="preserve">
    <value>Misc</value>
  </data>
  <data name="Guardian" xml:space="preserve">
    <value>Guardian</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>Insurance</value>
  </data>
  <data name="AmbientSolutionRoute" xml:space="preserve">
    <value>/ambientsolution</value>
  </data>
  <data name="LoadingUserData" xml:space="preserve">
    <value>LoadingUserData</value>
  </data>
  <data name="UserDataNotLoaded" xml:space="preserve">
    <value>UserDataNotLoaded</value>
  </data>
  <data name="EditUser" xml:space="preserve">
    <value>EditUser</value>
  </data>
  <data name="UserInformationForm" xml:space="preserve">
    <value>UserInformationForm</value>
  </data>
  <data name="UserInformationFormHeader" xml:space="preserve">
    <value>UserInformationFormHeader</value>
  </data>
  <data name="UserID" xml:space="preserve">
    <value>UserID</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>UserName</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>FirstName</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>LastName</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>PhoneNumber</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>DateOfBirth</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="TeyaHealth" xml:space="preserve">
    <value>Teya Health</value>
  </data>
  <data name="SearchUsername" xml:space="preserve">
    <value>Search by Username</value>
  </data>
  <data name="AddMember" xml:space="preserve">
    <value>Add Member</value>
  </data>
  <data name="NoMemberSelected" xml:space="preserve">
    <value>NoMemberSelected</value>
  </data>
  <data name="PatientsPagePath" xml:space="preserve">
    <value>/patients/</value>
  </data>
  <data name="PatientsPathTemplate" xml:space="preserve">
    <value>/patients/{0}</value>
  </data>
  <data name="ManagYourTeyaAccount" xml:space="preserve">
    <value>Manag Your Teya Account</value>
  </data>
  <data name="SignOut" xml:space="preserve">
    <value>Sign Out</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>Ok</value>
  </data>
  <data name="GusestUser" xml:space="preserve">
    <value>U</value>
  </data>
  <data name="ManageProfile" xml:space="preserve">
    <value>/manageprofile</value>
  </data>
  <data name="authentication/logout" xml:space="preserve">
    <value>authentication/logout</value>
  </data>
  <data name="UsernameClaim" xml:space="preserve">
    <value>preferred_username</value>
  </data>
  <data name="NameClaim" xml:space="preserve">
    <value>name</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>stop_circle</value>
  </data>
  <data name="Mic" xml:space="preserve">
    <value>mic</value>
  </data>
  <data name="Play" xml:space="preserve">
    <value>play_arrow</value>
  </data>
  <data name="Pause" xml:space="preserve">
    <value>pause</value>
  </data>
  <data name="NoUsersFound" xml:space="preserve">
    <value>NoUsersFound</value>
  </data>
  <data name="SearchUsers" xml:space="preserve">
    <value>SearchUsers</value>
  </data>
  <data name="SearchByEmail" xml:space="preserve">
    <value>SearchByEmail</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="InviteUserPath" xml:space="preserve">
    <value>/inviteuser</value>
  </data>
  <data name="UpdateUserPath" xml:space="preserve">
    <value>/updateuser</value>
  </data>
  <data name="UserManagementHeading" xml:space="preserve">
    <value>UserManagement</value>
  </data>
  <data name="ManageId" xml:space="preserve">
    <value>id</value>
  </data>
  <data name="ManageDisplayName" xml:space="preserve">
    <value>displayName</value>
  </data>
  <data name="ManageGivenName" xml:space="preserve">
    <value>givenName</value>
  </data>
  <data name="ManageSurname" xml:space="preserve">
    <value>surname</value>
  </data>
  <data name="ManageMail" xml:space="preserve">
    <value>mail</value>
  </data>
  <data name="ManageUserType" xml:space="preserve">
    <value>userType</value>
  </data>
  <data name="ManageJobTitle" xml:space="preserve">
    <value>jobTitle</value>
  </data>
  <data name="ManageCompanyName" xml:space="preserve">
    <value>companyName</value>
  </data>
  <data name="ManageDepartment" xml:space="preserve">
    <value>department</value>
  </data>
  <data name="ManageOfficeLocation" xml:space="preserve">
    <value>officeLocation</value>
  </data>
  <data name="ManageMobilePhone" xml:space="preserve">
    <value>mobilePhone</value>
  </data>
  <data name="ManageStreetAddress" xml:space="preserve">
    <value>streetAddress</value>
  </data>
  <data name="ManageCity" xml:space="preserve">
    <value>city</value>
  </data>
  <data name="ManageState" xml:space="preserve">
    <value>state</value>
  </data>
  <data name="ManagePostalCode" xml:space="preserve">
    <value>postalCode</value>
  </data>
  <data name="ManageCountry" xml:space="preserve">
    <value>country</value>
  </data>
  <data name="ManageExtensionCompanyName" xml:space="preserve">
    <value>extensionCompanyName</value>
  </data>
  <data name="ErrorDeletingMember" xml:space="preserve">
    <value>ErrorDeletingMember</value>
  </data>
  <data name="ErrorSubmittingMemberData" xml:space="preserve">
    <value>ErrorSubmittingMemberData</value>
  </data>
  <data name="MemberUpdated" xml:space="preserve">
    <value>MemberUpdated</value>
  </data>
  <data name="MemberRegistered" xml:space="preserve">
    <value>MemberRegistered</value>
  </data>
  <data name="RecordsUrl" xml:space="preserve">
    <value>RecordsUrl</value>
  </data>
  <data name="AudioBaseUrl" xml:space="preserve">
    <value>AudioBaseUrl</value>
  </data>
  <data name="TeyaRecorder" xml:space="preserve">
    <value>Teya Recorder</value>
  </data>
  <data name="EmployerDetails" xml:space="preserve">
    <value>Employer Details</value>
  </data>
  <data name="GuardianDetails" xml:space="preserve">
    <value>GuardianDetails</value>
  </data>
  <data name="StatsDetails" xml:space="preserve">
    <value>StatsDetails</value>
  </data>
  <data name="InsuranceDetails" xml:space="preserve">
    <value>InsuranceDetails</value>
  </data>
  <data name="MiscellaneousDetails" xml:space="preserve">
    <value>MiscellaneousDetails</value>
  </data>
  <data name="ExceptionOccurred" xml:space="preserve">
    <value>ExceptionOccurred</value>
  </data>
  <data name="GetByIdFailed" xml:space="preserve">
    <value>GetByIdFailed</value>
  </data>
  <data name="ErrorFetchingMemberById" xml:space="preserve">
    <value>ErrorFetchingMemberById</value>
  </data>
  <data name="FailedToUpdateUser" xml:space="preserve">
    <value>FailedToUpdateUser</value>
  </data>
  <data name="PRODUCT" xml:space="preserve">
    <value>PRODUCT</value>
  </data>
  <data name="EHRINTEGRATIONS" xml:space="preserve">
    <value>EHR INTEGRATIONS</value>
  </data>
  <data name="ABOUT" xml:space="preserve">
    <value>ABOUT</value>
  </data>
  <data name="CONTACTUS" xml:space="preserve">
    <value>CONTACT US</value>
  </data>
  <data name="LOGIN" xml:space="preserve">
    <value>LOGIN</value>
  </data>
  <data name="FacilityRegistered" xml:space="preserve">
    <value>FacilityRegistered</value>
  </data>
  <data name="ErrorSubmittingFacilityData" xml:space="preserve">
    <value>ErrorSubmittingFacilityData</value>
  </data>
  <data name="ErrorFetchingComplaints" xml:space="preserve">
    <value>Error Processing Complaints</value>
  </data>
  <data name="OrganizationNotFound" xml:space="preserve">
    <value>OrganizationNotFound</value>
  </data>
</root>