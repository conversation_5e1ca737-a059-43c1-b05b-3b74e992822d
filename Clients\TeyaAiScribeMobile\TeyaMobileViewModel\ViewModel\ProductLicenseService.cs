﻿using Microsoft.Extensions.Localization;
using DotNetEnv;
using Azure.Core;
using System.Text;
using System.Text.Json;
using TeyaMobileViewModel.TeyaUIViewModelResources;
using Microsoft.Extensions.Configuration;
using TeyaMobileModel.Model;
using System.Net.Http.Json;

namespace TeyaMobileViewModel.ViewModel
{
    public class ProductLicenseService : IProductLicenseService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _MemberService;
        private readonly ITokenService _tokenService;

        public ProductLicenseService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }
        public async Task<List<ProductLicense>> GetProductsLicenseAsync()
        {
            var accessToken = _tokenService.AccessToken;
            var apiurl = $"{_MemberService}/api/Licenses";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiurl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<ProductLicense>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["LicenseRetrievalFailure"]);
            }
        }

        public async Task UpdateLicenseAccessAsync(List<ProductLicense> licenseAccessUpdates)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiurl = $"{_MemberService}/api/Licenses/updateAccess";
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiurl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                var bodyContent = JsonSerializer.Serialize(licenseAccessUpdates);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new HttpRequestException(_localizer["UpdateAccessFailure"], ex);
            }
        }
    }
}