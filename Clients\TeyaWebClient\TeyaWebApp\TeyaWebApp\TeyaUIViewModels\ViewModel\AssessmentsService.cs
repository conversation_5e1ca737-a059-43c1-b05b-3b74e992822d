﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using static System.Net.WebRequestMethods;

namespace TeyaUIViewModels.ViewModel
{
    public class AssessmentsService : IAssessmentsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly ILogger<AssessmentsService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public AssessmentsService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ILogger<AssessmentsService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<AssessmentsData>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/Assessments/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<AssessmentsData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<AssessmentsData>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/Assessments/{id}/IsActive/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<AssessmentsData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<string>> GetDiagnosisListByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            try
            {
                var assessments = await GetAllByIdAndIsActiveAsync(id, OrgID, false);
                return assessments?
                    .Where(a => !string.IsNullOrEmpty(a.Diagnosis))
                    .Select(a => a.Diagnosis)
                    .Distinct()
                    .ToList() ?? new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingDiagnosis"]);
                throw;
            }
        }

        public async Task AddAssessmentsAsync(List<AssessmentsData> medicalHistories, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/Assessments/AddAssessments/{OrgID}/{Subscription}";

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task UpdateAssessmentsAsync(AssessmentsData _Assessments, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/Assessments/{_Assessments.PatientId}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_Assessments);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdateAssessmentsListAsync(List<AssessmentsData> medicalHistories, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/Assessments/UpdateAssessmentsList/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }
        public async Task<Dictionary<string, List<string>>> GetAllMedicationsRelatedToAssessments(Guid PatientId, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/Assessments/{PatientId}/AssesmentsRelatedMedications/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var medicationList = await response.Content.ReadFromJsonAsync<List<string>>();
                return ProcessMedications(medicationList ?? new List<string>()); // Convert list to hashmap
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }
        private Dictionary<string, List<string>> ProcessMedications(List<string> medicationList)
        {
            var medicationMap = new Dictionary<string, List<string>>();

            foreach (var item in medicationList)
            {
                var parts = item.Split("==", StringSplitOptions.TrimEntries);
                if (parts.Length == 2) // Ensure it has a valid format
                {
                    string key = parts[0];  // Condition (before "==")
                    string value = parts[1]; // Medication (after "==")

                    if (!medicationMap.ContainsKey(key))
                    {
                        medicationMap[key] = new List<string>();
                    }
                    medicationMap[key].Add(value);
                }
            }

            return medicationMap;
        }
        public async Task DeleteAssessmentsByEntityAsync(AssessmentsData _Assessments, Guid? OrgID, bool Subscription)
        {
            if (_Assessments == null)
            {
                throw new ArgumentNullException(nameof(_Assessments), _localizer["InvalidRecord"]);
            }

            var apiUrl = $"{_EncounterNotes}/api/Assessments/DeleteAssessments/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_Assessments);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DeleteLogError"]);
            }
        }

    }
}