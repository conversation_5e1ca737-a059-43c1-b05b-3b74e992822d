﻿using Azure.Communication.Email;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileViewModel.ViewModel
{
    public interface ICommunicationService
    {
        Task<EmailSendOperation> MailService(
            string senderAddress,
            string subject,
            string plainTextContent,
            List<string> recipientAddresses);
        Task<EmailSendOperation> SendHtmlEmailService(
            string senderAddress,
            string subject,
            string htmlContent,
            List<string> recipientAddresses);
    }
}
